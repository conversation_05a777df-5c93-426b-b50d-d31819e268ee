// Gestion des formulaires et modales
document.addEventListener('DOMContentLoaded', function() {
    // Bouton Nouvelle Vidange
    const btnNouvelleVidange = document.getElementById('btnNouvelleVidange');
    const verificationSection = document.getElementById('verificationSection');
    const ajoutVidangeSection = document.getElementById('ajoutVidangeSection');

    if (btnNouvelleVidange) {
        btnNouvelleVidange.addEventListener('click', function() {
            verificationSection.style.display = 'block';
            ajoutVidangeSection.style.display = 'none';
        });
    }

    // Formulaire de vérification
    const verificationForm = document.getElementById('verificationForm');
    if (verificationForm) {
        verificationForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const matricule = document.getElementById('matricule').value;
            verifierVehicule(matricule);
        });
    }

    // Bouton Annuler Vérification
    const btnAnnulerVerification = document.getElementById('btnAnnulerVerification');
    if (btnAnnulerVerification) {
        btnAnnulerVerification.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            document.getElementById('vehiculeInfoDisplay').style.display = 'none';
            document.getElementById('btnConfirmerVehicule').style.display = 'none';
            document.getElementById('matricule').value = '';
        });
    }

    // Bouton Confirmer Véhicule
    const btnConfirmerVehicule = document.getElementById('btnConfirmerVehicule');
    if (btnConfirmerVehicule) {
        btnConfirmerVehicule.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            ajoutVidangeSection.style.display = 'block';
        });
    }

    // Formulaire d'ajout de vidange
    const ajoutVidangeForm = document.getElementById('ajoutVidangeForm');
    if (ajoutVidangeForm) {
        ajoutVidangeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            ajouterVidange(formData);
        });
    }

    // Gestion des pièces remplacées
    const btnAjouterPiece = document.getElementById('btnAjouterPiece');
    const autrePieceInput = document.getElementById('autrePiece');
    const piecesContainer = document.getElementById('piecesContainer');

    if (btnAjouterPiece && autrePieceInput && piecesContainer) {
        btnAjouterPiece.addEventListener('click', function() {
            const pieceText = autrePieceInput.value.trim();
            if (pieceText) {
                const newId = 'piece' + (piecesContainer.children.length + 1);
                const div = document.createElement('div');
                div.className = 'form-check';
                div.innerHTML = `
                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" 
                           value="${pieceText}" id="${newId}" checked>
                    <label class="form-check-label" for="${newId}">${pieceText}</label>
                `;
                piecesContainer.appendChild(div);
                autrePieceInput.value = '';
            }
        });
    }

    // Initialiser les gestionnaires d'événements pour les boutons dans le tableau
    initTableButtons();

    // Système de notification pour les entretiens
    function verifierProchainEntretien() {
        fetch('/api/entretiens/verification_km')
            .then(response => response.json())
            .then(data => {
                data.forEach(vehicule => {
                    const difference = vehicule.kilometrage_prochain - vehicule.kilometrage_actuel;
                    if (difference <= 1000) {
                        const alertIcon = document.getElementById('notificationIcon');
                        const badge = document.getElementById('notificationBadge');
                        
                        alertIcon.style.display = 'flex';
                        alertIcon.classList.add('active');
                        
                        if (difference <= 500) {
                            alertIcon.classList.add('urgent');
                            badge.style.display = 'block';
                            badge.textContent = difference + ' km';
                        } else {
                            alertIcon.classList.remove('urgent');
                            badge.style.display = 'block';
                            badge.textContent = difference + ' km';
                        }

                        // Mise à jour du tooltip
                        const message = `Attention: ${vehicule.matricule} - Prochain entretien dans ${difference} km`;
                        if (!alertIcon._tippy) {
                            tippy(alertIcon, {
                                content: message,
                                placement: 'bottom',
                                theme: 'military',
                                animation: 'scale'
                            });
                        } else {
                            alertIcon._tippy.setContent(message);
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Erreur lors de la vérification des entretiens:', error);
            });
    }

    // Vérifier les entretiens toutes les 5 minutes
    verifierProchainEntretien();
    setInterval(verifierProchainEntretien, 300000);
});

// Vérification du véhicule
function verifierVehicule(matricule) {
    // Valider le matricule avant l'envoi
    if (!matricule || matricule.trim() === '') {
        alert('Veuillez entrer un matricule');
        return;
    }

    const infoDisplay = document.getElementById('vehiculeInfoDisplay');
    const detailsDisplay = document.getElementById('vehiculeDetailsDisplay');
    const btnConfirmer = document.getElementById('btnConfirmerVehicule');
    const vehiculeIdInput = document.getElementById('vehicule_id_ajout');
    const ajoutVidangeMatricule = document.getElementById('ajoutVidangeMatricule');

    // Afficher un indicateur de chargement
    infoDisplay.style.display = 'block';
    infoDisplay.className = 'alert alert-info mt-3';
    detailsDisplay.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p>Recherche en cours...</p></div>';

    fetch('/verifier_vehicule?matricule=' + encodeURIComponent(matricule.trim()))
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau');
            }
            return response.json();
        })
        .then(data => {

            infoDisplay.style.display = 'block';

            if (data.success && data.vehicule) {
                detailsDisplay.innerHTML = `
                    <strong>Matricule:</strong> ${data.vehicule.matricule}<br>
                    <strong>Type:</strong> ${data.vehicule.type_vehicule}<br>
                    <strong>Unité:</strong> ${data.vehicule.unite || 'Non assigné'}<br>
                    <strong>Marque:</strong> ${data.vehicule.marque}
                `;
                btnConfirmer.style.display = 'inline-block';
                vehiculeIdInput.value = data.vehicule.id;
                ajoutVidangeMatricule.textContent = data.vehicule.matricule;

                infoDisplay.className = 'alert alert-success mt-3';
            } else {
                detailsDisplay.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Aucun véhicule trouvé</strong><br>
                        ${data.message || 'Aucun véhicule trouvé avec ce matricule.'}
                    </div>`;
                btnConfirmer.style.display = 'none';
                infoDisplay.className = 'alert alert-danger mt-3';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            detailsDisplay.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Erreur</strong><br>
                    Une erreur est survenue lors de la vérification du véhicule. Veuillez réessayer.
                </div>`;
            btnConfirmer.style.display = 'none';
            infoDisplay.className = 'alert alert-danger mt-3';
        });
}

// Ajout d'une vidange
function ajouterVidange(formData) {
    fetch('/entretiens/ajouter', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            window.location.reload();
        } else {
            throw new Error('Erreur lors de l\'ajout de la vidange');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert(error.message);
    });
}

// Mise à jour du kilométrage
function mettreAJourKilometrage(vehiculeId) {
    const updateSection = document.getElementById('updateKilometrageSection');
    if (updateSection) {
        document.getElementById('vehicule_id_update').value = vehiculeId;
        updateSection.style.display = 'block';
        window.scrollTo({ top: updateSection.offsetTop, behavior: 'smooth' });
    }
}

// Supprimer un entretien
function supprimerEntretien(entretienId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet entretien ?')) {
        fetch(`/entretiens/supprimer/${entretienId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                throw new Error(data.error || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la suppression de l\'entretien');
        });
    }
}

// Initialisation des boutons du tableau
function initTableButtons() {
    // Gestion de la mise à jour du kilométrage
    const formMiseAJourKilometrage = document.getElementById('formMiseAJourKilometrage');
    if (formMiseAJourKilometrage) {
        formMiseAJourKilometrage.addEventListener('submit', function(e) {
            e.preventDefault();
            const vehiculeId = document.getElementById('vehicule_id_update').value;
            const kilometrage = document.getElementById('nouveau_kilometrage').value;

            fetch(`/mise_a_jour_kilometrage/${vehiculeId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `kilometrage=${kilometrage}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    throw new Error(data.error || 'Erreur lors de la mise à jour');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erreur lors de la mise à jour du kilométrage');
            });
        });
    }

    // Gestion du bouton d'annulation de mise à jour du kilométrage
    const btnCancelKilometrage = document.getElementById('btnCancelKilometrage');
    if (btnCancelKilometrage) {
        btnCancelKilometrage.addEventListener('click', function() {
            document.getElementById('updateKilometrageSection').style.display = 'none';
        });
    }

    // Gestionnaire d'événements pour les boutons du tableau
    document.querySelector('#vidangesTable tbody').addEventListener('click', function(e) {
        const target = e.target.closest('button');
        if (!target) return;

        if (target.classList.contains('btn-update-km')) {
            const vehiculeId = target.getAttribute('data-vehicule-id');
            mettreAJourKilometrage(vehiculeId);
        } else if (target.classList.contains('btn-delete-entretien')) {
            const entretienId = target.getAttribute('data-entretien-id');
            supprimerEntretien(entretienId);
        }
    });
}

// Enhanced Light Military Alert System
function checkVidangeAlerts() {
    const alertIcon = document.getElementById('notificationIcon');
    const notificationBadge = document.getElementById('notificationBadge');
    let alertVehicles = [];
    let urgent = false;

    console.log('🔍 Vérification des alertes de vidange...');

    // Vérifier que les éléments existent
    if (!alertIcon) {
        console.warn('❌ Element notificationIcon non trouvé');
        return [];
    }

    // Lire les données du tableau
    const table = document.querySelector('#vidangesTable');
    if (!table) {
        console.warn('❌ Tableau vidangesTable non trouvé');
        return [];
    }

    const rows = document.querySelectorAll('#vidangesTable tbody tr');
    console.log(`📊 Nombre de lignes trouvées: ${rows.length}`);

    rows.forEach((row, index) => {
        if (row.cells.length >= 5) {
            const matricule = row.cells[0].textContent.trim();
            const currentKmText = row.cells[3].textContent.trim();
            const nextKmText = row.cells[4].textContent.trim();

            console.log(`🚗 Ligne ${index + 1}: ${matricule} - KM actuel: "${currentKmText}" - KM prochain: "${nextKmText}"`);

            const currentKm = parseInt(currentKmText.replace(/\D/g, ''));
            const nextKm = parseInt(nextKmText.replace(/\D/g, ''));

            if (!isNaN(currentKm) && !isNaN(nextKm)) {
                const diff = nextKm - currentKm;
                console.log(`📏 Différence pour ${matricule}: ${diff} km`);

                if (diff <= 1000) {
                    alertVehicles.push({matricule, diff, currentKm, nextKm});
                    console.log(`⚠️ Alerte ajoutée pour ${matricule}: ${diff} km restants`);
                    if (diff <= 500) {
                        urgent = true;
                        console.log(`🚨 URGENT pour ${matricule}!`);
                    }
                }
            } else {
                console.warn(`❌ Valeurs invalides pour ${matricule}: currentKm=${currentKm}, nextKm=${nextKm}`);
            }
        }
    });

    console.log(`🎯 Total alertes détectées: ${alertVehicles.length}`, alertVehicles);

    if (alertVehicles.length > 0) {
        console.log('✅ Affichage de l\'icône d\'alerte');
        alertIcon.style.display = 'flex';
        alertIcon.classList.add('active');

        if (urgent) {
            console.log('🚨 Mode urgent activé');
            alertIcon.classList.add('urgent');
        } else {
            alertIcon.classList.remove('urgent');
        }

        // Afficher le badge avec le nombre d'alertes
        if (notificationBadge) {
            notificationBadge.style.display = 'block';
            notificationBadge.textContent = alertVehicles.length;
            console.log(`🔢 Badge mis à jour: ${alertVehicles.length}`);
        }

        // Animation pulse pour attirer l'attention
        alertIcon.classList.add('pulse');
        setTimeout(() => {
            alertIcon.classList.remove('pulse');
        }, 1000);

        // Message pour le tooltip
        const message = alertVehicles.length === 1
            ? `1 véhicule nécessite une vidange`
            : `${alertVehicles.length} véhicules nécessitent une vidange`;
        alertIcon.setAttribute('data-bs-original-title', message);

    } else {
        console.log('❌ Aucune alerte - masquage de l\'icône');
        alertIcon.style.display = 'none';
        alertIcon.classList.remove('active', 'urgent', 'pulse');
        if (notificationBadge) {
            notificationBadge.style.display = 'none';
        }
    }

    return alertVehicles;
}

// Fonction pour afficher le modal d'alertes
function showAlertModal() {
    const modalBody = document.getElementById('alertModalBody');

    // Récupérer les alertes actuelles avec les données complètes du tableau
    const alertVehicles = getDetailedAlertVehicles();

    console.log('🎯 Affichage du modal avec', alertVehicles.length, 'alertes');

    // Générer le contenu du modal
    if (alertVehicles.length > 0) {
        modalBody.innerHTML = alertVehicles.map(vehicle => {
            const isOverdue = vehicle.diff <= 0;
            const isUrgent = vehicle.diff <= 500 && vehicle.diff > 0;

            let statusText, statusClass, statusLabel;

            if (isOverdue) {
                statusText = 'Dépassé';
                statusClass = 'overdue';
                statusLabel = 'Vidange en retard';
            } else if (isUrgent) {
                statusText = 'Urgent';
                statusClass = 'urgent';
                statusLabel = `Échéance dans ${vehicle.diff} km`;
            } else {
                statusText = 'À prévoir';
                statusClass = 'normal';
                statusLabel = `Échéance dans ${vehicle.diff} km`;
            }

            return `
                <div class="vehicle-alert-card ${statusClass}" onclick="highlightVehicleInTable('${vehicle.matricule}')">
                    <div class="vehicle-icon ${statusClass}">
                        <i class="fas fa-car"></i>
                    </div>
                    <div class="vehicle-info">
                        <div class="vehicle-matricule">${vehicle.matricule}</div>
                        <div class="vehicle-unite">
                            <i class="fas fa-building me-1"></i>${vehicle.unite || 'Unité non assignée'}
                        </div>
                        <div class="vehicle-km-info">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>${vehicle.currentKm.toLocaleString()} km → ${vehicle.nextKm.toLocaleString()} km</span>
                        </div>
                    </div>
                    <div class="vehicle-status">
                        <div class="status-badge ${statusClass}">${statusText}</div>
                        <div class="km-remaining">${statusLabel}</div>
                    </div>
                </div>
            `;
        }).join('');
    } else {
        modalBody.innerHTML = `
            <div class="no-alerts">
                <i class="fas fa-check-circle"></i>
                <h6>Aucune alerte de vidange</h6>
                <p>Tous vos véhicules sont à jour pour leurs entretiens.</p>
            </div>
        `;
    }

    // Afficher le modal
    const modalElement = document.getElementById('alertModal');

    // Détruire toute instance existante du modal
    const existingModal = bootstrap.Modal.getInstance(modalElement);
    if (existingModal) {
        existingModal.dispose();
    }

    // Créer une nouvelle instance du modal
    const modal = new bootstrap.Modal(modalElement, {
        backdrop: 'static',  // Empêche la fermeture en cliquant sur le backdrop
        keyboard: true,      // Permet la fermeture avec Échap
        focus: true         // Focus automatique
    });

    // S'assurer que le modal est au premier plan
    modalElement.style.zIndex = '10000';

    modal.show();

    console.log('✅ Modal affiché avec z-index forcé');
}

// Fonction pour récupérer les données détaillées des véhicules avec alertes
function getDetailedAlertVehicles() {
    let alertVehicles = [];
    const rows = document.querySelectorAll('#vidangesTable tbody tr');

    rows.forEach(row => {
        if (row.cells.length >= 5) {
            const matricule = row.cells[0].textContent.trim();
            const unite = row.cells[1].textContent.trim();
            const currentKmText = row.cells[3].textContent.trim();
            const nextKmText = row.cells[4].textContent.trim();

            const currentKm = parseInt(currentKmText.replace(/\D/g, ''));
            const nextKm = parseInt(nextKmText.replace(/\D/g, ''));

            if (!isNaN(currentKm) && !isNaN(nextKm)) {
                const diff = nextKm - currentKm;
                if (diff <= 1000) {
                    alertVehicles.push({
                        matricule,
                        unite,
                        diff,
                        currentKm,
                        nextKm
                    });
                }
            }
        }
    });

    // Trier par urgence (les plus urgents en premier)
    alertVehicles.sort((a, b) => a.diff - b.diff);

    return alertVehicles;
}

// Fonction pour fermer le modal manuellement
function closeAlertModal() {
    console.log('🔄 Tentative de fermeture du modal...');

    const modalElement = document.getElementById('alertModal');
    const modal = bootstrap.Modal.getInstance(modalElement);

    if (modal) {
        console.log('✅ Instance Bootstrap trouvée, fermeture...');
        modal.hide();
    } else {
        console.log('⚠️ Aucune instance Bootstrap, fermeture manuelle...');
        // Fallback: fermeture manuelle complète
        modalElement.classList.remove('show', 'fade');
        modalElement.style.display = 'none';
        modalElement.setAttribute('aria-hidden', 'true');
        modalElement.removeAttribute('aria-modal');

        // Nettoyer le body
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // Supprimer tous les backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
    }

    console.log('✅ Modal fermé');
}

// Fonction pour surligner un véhicule dans le tableau
function highlightVehicleInTable(matricule) {
    // Supprimer les anciens surlignages
    document.querySelectorAll('#vidangesTable tbody tr').forEach(row => {
        row.classList.remove('table-warning');
    });

    // Trouver et surligner la ligne correspondante
    const rows = document.querySelectorAll('#vidangesTable tbody tr');
    rows.forEach(row => {
        if (row.cells[0] && row.cells[0].textContent.trim() === matricule) {
            row.classList.add('table-warning');
            row.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // Retirer le surlignage après 3 secondes
            setTimeout(() => {
                row.classList.remove('table-warning');
            }, 3000);
        }
    });

    // Fermer le dropdown
    hideAlertDropdown();
}

// Initialize modernized alert system
document.addEventListener('DOMContentLoaded', function() {
    const alertIcon = document.getElementById('notificationIcon');

    if (alertIcon) {
        // Initialize Bootstrap tooltip
        const tooltip = new bootstrap.Tooltip(alertIcon, {
            placement: 'bottom',
            trigger: 'hover',
            animation: true,
            template: `
                <div class="tooltip military-tooltip" role="tooltip">
                    <div class="tooltip-arrow"></div>
                    <div class="tooltip-inner" style="background: linear-gradient(145deg, #E8F0E0, #C8D4B8);
                                                    color: #4A5D23;
                                                    border: 1px solid rgba(125, 140, 101, 0.2);
                                                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    </div>
                </div>
            `
        });

        // Gestionnaire de clic pour afficher le modal
        alertIcon.addEventListener('click', function(e) {
            e.stopPropagation();
            console.log('🔄 Clic sur l\'icône de notification détecté');

            // Vérifier si la fonction existe
            if (typeof showAlertModal === 'function') {
                console.log('✅ Fonction showAlertModal trouvée, appel...');
                showAlertModal();
            } else {
                console.error('❌ Fonction showAlertModal non trouvée');
                // Fallback: afficher un modal simple
                const modalElement = document.getElementById('alertModal');
                if (modalElement) {
                    console.log('🔄 Tentative d\'affichage direct du modal...');
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                } else {
                    console.error('❌ Element alertModal non trouvé');
                }
            }
        });

        // Vérification initiale
        setTimeout(() => {
            checkVidangeAlerts();
        }, 500);

        // Observer les changements du tableau
        const table = document.querySelector('#vidangesTable tbody');
        if (table) {
            const observer = new MutationObserver(() => {
                setTimeout(checkVidangeAlerts, 100);
            });
            observer.observe(table, {childList: true, subtree: true, characterData: true});
        }
    }

    // Gestionnaire pour s'assurer que le modal fonctionne correctement
    const alertModal = document.getElementById('alertModal');
    if (alertModal) {
        alertModal.addEventListener('shown.bs.modal', function () {
            console.log('✅ Modal complètement affiché et interactif');
        });

        alertModal.addEventListener('hidden.bs.modal', function () {
            console.log('✅ Modal fermé');
        });

        // Gestionnaire pour fermer en cliquant sur le backdrop
        alertModal.addEventListener('click', function (e) {
            if (e.target === alertModal) {
                closeAlertModal();
            }
        });
    }

    // Gestionnaire global pour la touche Échap
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modalElement = document.getElementById('alertModal');
            if (modalElement && modalElement.classList.contains('show')) {
                closeAlertModal();
            }
        }
    });
});
