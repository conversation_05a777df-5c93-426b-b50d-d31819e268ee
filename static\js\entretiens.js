// Gestion des formulaires et modales
document.addEventListener('DOMContentLoaded', function() {
    // Bouton Nouvelle Vidange
    const btnNouvelleVidange = document.getElementById('btnNouvelleVidange');
    const verificationSection = document.getElementById('verificationSection');
    const ajoutVidangeSection = document.getElementById('ajoutVidangeSection');

    if (btnNouvelleVidange) {
        btnNouvelleVidange.addEventListener('click', function() {
            verificationSection.style.display = 'block';
            ajoutVidangeSection.style.display = 'none';
        });
    }

    // Formulaire de vérification
    const verificationForm = document.getElementById('verificationForm');
    if (verificationForm) {
        verificationForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const matricule = document.getElementById('matricule').value;
            verifierVehicule(matricule);
        });
    }

    // Bouton Annuler Vérification
    const btnAnnulerVerification = document.getElementById('btnAnnulerVerification');
    if (btnAnnulerVerification) {
        btnAnnulerVerification.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            document.getElementById('vehiculeInfoDisplay').style.display = 'none';
            document.getElementById('btnConfirmerVehicule').style.display = 'none';
            document.getElementById('matricule').value = '';
        });
    }

    // Bouton Confirmer Véhicule
    const btnConfirmerVehicule = document.getElementById('btnConfirmerVehicule');
    if (btnConfirmerVehicule) {
        btnConfirmerVehicule.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            ajoutVidangeSection.style.display = 'block';
        });
    }

    // Formulaire d'ajout de vidange
    const ajoutVidangeForm = document.getElementById('ajoutVidangeForm');
    if (ajoutVidangeForm) {
        ajoutVidangeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            ajouterVidange(formData);
        });
    }

    // Gestion des pièces remplacées
    const btnAjouterPiece = document.getElementById('btnAjouterPiece');
    const autrePieceInput = document.getElementById('autrePiece');
    const piecesContainer = document.getElementById('piecesContainer');

    if (btnAjouterPiece && autrePieceInput && piecesContainer) {
        btnAjouterPiece.addEventListener('click', function() {
            const pieceText = autrePieceInput.value.trim();
            if (pieceText) {
                const newId = 'piece' + (piecesContainer.children.length + 1);
                const div = document.createElement('div');
                div.className = 'form-check';
                div.innerHTML = `
                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" 
                           value="${pieceText}" id="${newId}" checked>
                    <label class="form-check-label" for="${newId}">${pieceText}</label>
                `;
                piecesContainer.appendChild(div);
                autrePieceInput.value = '';
            }
        });
    }

    // Initialiser les gestionnaires d'événements pour les boutons dans le tableau
    initTableButtons();

    // Système de notification pour les entretiens
    function verifierProchainEntretien() {
        fetch('/api/entretiens/verification_km')
            .then(response => response.json())
            .then(data => {
                data.forEach(vehicule => {
                    const difference = vehicule.kilometrage_prochain - vehicule.kilometrage_actuel;
                    if (difference <= 1000) {
                        const alertIcon = document.getElementById('notificationIcon');
                        const badge = document.getElementById('notificationBadge');
                        
                        alertIcon.style.display = 'flex';
                        alertIcon.classList.add('active');
                        
                        if (difference <= 500) {
                            alertIcon.classList.add('urgent');
                            badge.style.display = 'block';
                            badge.textContent = difference + ' km';
                        } else {
                            alertIcon.classList.remove('urgent');
                            badge.style.display = 'block';
                            badge.textContent = difference + ' km';
                        }

                        // Mise à jour du tooltip
                        const message = `Attention: ${vehicule.matricule} - Prochain entretien dans ${difference} km`;
                        if (!alertIcon._tippy) {
                            tippy(alertIcon, {
                                content: message,
                                placement: 'bottom',
                                theme: 'military',
                                animation: 'scale'
                            });
                        } else {
                            alertIcon._tippy.setContent(message);
                        }
                    }
                });
            })
            .catch(error => {
                console.error('Erreur lors de la vérification des entretiens:', error);
            });
    }

    // Vérifier les entretiens toutes les 5 minutes
    verifierProchainEntretien();
    setInterval(verifierProchainEntretien, 300000);
});

// Vérification du véhicule
function verifierVehicule(matricule) {
    // Valider le matricule avant l'envoi
    if (!matricule || matricule.trim() === '') {
        alert('Veuillez entrer un matricule');
        return;
    }

    const infoDisplay = document.getElementById('vehiculeInfoDisplay');
    const detailsDisplay = document.getElementById('vehiculeDetailsDisplay');
    const btnConfirmer = document.getElementById('btnConfirmerVehicule');
    const vehiculeIdInput = document.getElementById('vehicule_id_ajout');
    const ajoutVidangeMatricule = document.getElementById('ajoutVidangeMatricule');

    // Afficher un indicateur de chargement
    infoDisplay.style.display = 'block';
    infoDisplay.className = 'alert alert-info mt-3';
    detailsDisplay.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div><p>Recherche en cours...</p></div>';

    fetch('/verifier_vehicule?matricule=' + encodeURIComponent(matricule.trim()))
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau');
            }
            return response.json();
        })
        .then(data => {

            infoDisplay.style.display = 'block';

            if (data.success && data.vehicule) {
                detailsDisplay.innerHTML = `
                    <strong>Matricule:</strong> ${data.vehicule.matricule}<br>
                    <strong>Type:</strong> ${data.vehicule.type_vehicule}<br>
                    <strong>Unité:</strong> ${data.vehicule.unite || 'Non assigné'}<br>
                    <strong>Marque:</strong> ${data.vehicule.marque}
                `;
                btnConfirmer.style.display = 'inline-block';
                vehiculeIdInput.value = data.vehicule.id;
                ajoutVidangeMatricule.textContent = data.vehicule.matricule;

                infoDisplay.className = 'alert alert-success mt-3';
            } else {
                detailsDisplay.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Aucun véhicule trouvé</strong><br>
                        ${data.message || 'Aucun véhicule trouvé avec ce matricule.'}
                    </div>`;
                btnConfirmer.style.display = 'none';
                infoDisplay.className = 'alert alert-danger mt-3';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            detailsDisplay.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Erreur</strong><br>
                    Une erreur est survenue lors de la vérification du véhicule. Veuillez réessayer.
                </div>`;
            btnConfirmer.style.display = 'none';
            infoDisplay.className = 'alert alert-danger mt-3';
        });
}

// Ajout d'une vidange
function ajouterVidange(formData) {
    fetch('/entretiens/ajouter', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            window.location.reload();
        } else {
            throw new Error('Erreur lors de l\'ajout de la vidange');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert(error.message);
    });
}

// Mise à jour du kilométrage
function mettreAJourKilometrage(vehiculeId) {
    const updateSection = document.getElementById('updateKilometrageSection');
    if (updateSection) {
        document.getElementById('vehicule_id_update').value = vehiculeId;
        updateSection.style.display = 'block';
        window.scrollTo({ top: updateSection.offsetTop, behavior: 'smooth' });
    }
}

// Supprimer un entretien
function supprimerEntretien(entretienId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet entretien ?')) {
        fetch(`/entretiens/supprimer/${entretienId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                throw new Error(data.error || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la suppression de l\'entretien');
        });
    }
}

// Initialisation des boutons du tableau
function initTableButtons() {
    // Gestion de la mise à jour du kilométrage
    const formMiseAJourKilometrage = document.getElementById('formMiseAJourKilometrage');
    if (formMiseAJourKilometrage) {
        formMiseAJourKilometrage.addEventListener('submit', function(e) {
            e.preventDefault();
            const vehiculeId = document.getElementById('vehicule_id_update').value;
            const kilometrage = document.getElementById('nouveau_kilometrage').value;

            fetch(`/mise_a_jour_kilometrage/${vehiculeId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `kilometrage=${kilometrage}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    throw new Error(data.error || 'Erreur lors de la mise à jour');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erreur lors de la mise à jour du kilométrage');
            });
        });
    }

    // Gestion du bouton d'annulation de mise à jour du kilométrage
    const btnCancelKilometrage = document.getElementById('btnCancelKilometrage');
    if (btnCancelKilometrage) {
        btnCancelKilometrage.addEventListener('click', function() {
            document.getElementById('updateKilometrageSection').style.display = 'none';
        });
    }

    // Gestionnaire d'événements pour les boutons du tableau
    document.querySelector('#vidangesTable tbody').addEventListener('click', function(e) {
        const target = e.target.closest('button');
        if (!target) return;

        if (target.classList.contains('btn-update-km')) {
            const vehiculeId = target.getAttribute('data-vehicule-id');
            mettreAJourKilometrage(vehiculeId);
        } else if (target.classList.contains('btn-delete-entretien')) {
            const entretienId = target.getAttribute('data-entretien-id');
            supprimerEntretien(entretienId);
        }
    });
}

// Enhanced Light Military Alert System
function checkNotification(kilometrageActuel, kilometrageProchainEntretien) {
    const alertIcon = document.getElementById('notificationIcon');
    const difference = kilometrageProchainEntretien - kilometrageActuel;

    if (difference <= 1000) {
        alertIcon.style.display = 'flex';
        alertIcon.classList.add('active');
        
        // Message formaté de manière claire
        const message = `Prochain entretien : ${difference} km restants`;
        alertIcon.setAttribute('data-bs-title', message);
        
        if (difference <= 500) {
            alertIcon.classList.add('urgent');
            // Ajout d'une classe pour l'animation de surbrillance
            alertIcon.classList.add('highlight-urgent');
        } else {
            alertIcon.classList.remove('urgent', 'highlight-urgent');
        }
    } else {
        alertIcon.style.display = 'none';
        alertIcon.classList.remove('active', 'urgent', 'highlight-urgent');
    }
}

// Initialize modernized alert system
document.addEventListener('DOMContentLoaded', function() {
    const alertIcon = document.getElementById('notificationIcon');
    
    if (alertIcon) {
        // Initialize Bootstrap tooltip with style personnalisé
        const tooltip = new bootstrap.Tooltip(alertIcon, {
            placement: 'bottom',
            trigger: 'hover',
            animation: true,
            template: `
                <div class="tooltip military-tooltip" role="tooltip">
                    <div class="tooltip-arrow"></div>
                    <div class="tooltip-inner" style="background: linear-gradient(145deg, #E8F0E0, #C8D4B8);
                                                    color: #4A5D23;
                                                    border: 1px solid rgba(125, 140, 101, 0.2);
                                                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    </div>
                </div>
            `
        });

        // Example usage (replace with actual data)
        const kilometrageActuel = 5000;
        const kilometrageProchainEntretien = 5800;
        checkNotification(kilometrageActuel, kilometrageProchainEntretien);
    }
});
