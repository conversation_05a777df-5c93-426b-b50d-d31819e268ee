from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from datetime import datetime, timedelta
from sqlalchemy import or_
import random  # Ajout de l'import random

app = Flask(__name__)
app.secret_key = 'votre_cle_secrete_ici'  # Nécessaire pour les messages flash et la session

# Import et initialisation de la base de données
from db import db, init_app, VehiculeGAR, Entretien
init_app(app)

# Définition des modèles de base de données
class Vehicule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    type_vehicule = db.Column(db.String(20), nullable=False)  # VL, PL, Engin chenillé, SA
    marque = db.Column(db.String(50), nullable=False)
    matricule = db.Column(db.String(50), nullable=False, unique=True)  # Anciennement 'modele', maintenant avec contrainte d'unicité
    type_panne = db.Column(db.String(100), nullable=False)
    date_panne = db.Column(db.Date, nullable=False)
    description = db.Column(db.Text, nullable=True)
    statut = db.Column(db.String(20), default='En panne')
    unite = db.Column(db.String(20), nullable=True)  # Unité militaire (13GAR, 14GAR, etc.)

    def to_dict(self):
        return {
            'id': self.id,
            'type_vehicule': self.type_vehicule,
            'marque': self.marque,
            'modele': self.matricule,  # Pour maintenir la compatibilité avec le code existant
            'matricule': self.matricule,  # Nouvelle clé
            'type_panne': self.type_panne,
            'date_panne': self.date_panne.strftime('%Y-%m-%d'),
            'description': self.description,
            'statut': self.statut,
            'unite': self.unite
        }

class VehiculeHistorique(db.Model):
    __tablename__ = 'vehicule_historique'

    id = db.Column(db.Integer, primary_key=True)
    vehicule_id = db.Column(db.Integer, db.ForeignKey('vehicule.id', ondelete='CASCADE'), nullable=False)
    date_changement = db.Column(db.DateTime, nullable=False, default=datetime.now)
    statut = db.Column(db.String(20), nullable=False)
    type_panne = db.Column(db.String(100), nullable=True)
    date_panne = db.Column(db.Date, nullable=True)
    description = db.Column(db.Text, nullable=True)

    # Relation avec le véhicule
    vehicule = db.relationship('Vehicule', backref=db.backref('historique', lazy=True, cascade="all, delete-orphan"))

    def to_dict(self):
        return {
            'id': self.id,
            'vehicule_id': self.vehicule_id,
            'date_changement': self.date_changement.strftime('%Y-%m-%d %H:%M:%S'),
            'date_panne': self.date_panne.strftime('%Y-%m-%d') if self.date_panne else None,
            'type_panne': self.type_panne,
            'statut': self.statut,
            'description': self.description
        }





# Types de véhicules et leurs marques
TYPES_VEHICULES = {
    'VL': ['Toyota', 'Atlas 3', 'Atlas 4', 'Atlas 5', 'VLRA', 'Hummer', 'Nissan'],
    'PL': ['JBD', 'Kaiser', 'Renault'],
    'Engin chenillé': ['M109 155mm', 'M110 203mm', 'Vulcan'],
    'SA': ['PULS', 'HIMARS', 'CESAR']
}

# Types de pannes mécaniques par type de véhicule
TYPES_PANNES = {
    'VL': [
        # Moteur
        'Fuite d\'huile moteur',
        'Surchauffe moteur',
        'Défaillance du système d\'injection',
        'Problème de démarrage',
        'Courroie de distribution cassée',
        'Joint de culasse défectueux',
        'Perte de puissance moteur',
        'Consommation excessive d\'huile',
        'Bruit anormal du moteur',
        'Fumée blanche à l\'échappement',
        'Fumée noire à l\'échappement',

        # Transmission
        'Embrayage défectueux',
        'Problème de transmission',
        'Boîte de vitesses bloquée',
        'Difficulté à passer les vitesses',
        'Bruit anormal de la transmission',
        'Fuite d\'huile de transmission',
        'Usure prématurée de l\'embrayage',

        # Freinage
        'Défaillance du système de freinage',
        'Usure des plaquettes de frein',
        'Disques de frein voilés',
        'Fuite de liquide de frein',
        'ABS défectueux',
        'Frein à main défaillant',

        # Direction et suspension
        'Problème de direction assistée',
        'Amortisseurs usés',
        'Rotules de suspension défectueuses',
        'Barre stabilisatrice cassée',
        'Craquements dans la suspension',
        'Alignement des roues incorrect',

        # Électrique
        'Batterie déchargée',
        'Alternateur défectueux',
        'Démarreur défaillant',
        'Court-circuit électrique',
        'Problème de faisceau électrique',
        'Défaillance des phares tactiques',
        'Système radio défectueux',

        # Carburant
        'Pompe à carburant défaillante',
        'Filtre à carburant colmaté',
        'Fuite du système d\'alimentation',
        'Injecteurs encrassés',
        'Problème de régulation de pression'
    ],

    'PL': [
        # Moteur
        'Défaillance du turbocompresseur',
        'Fuite du circuit de refroidissement',
        'Problème de suralimentation',
        'Joint de culasse défectueux',
        'Surchauffe moteur diesel',
        'Fuite d\'huile moteur',
        'Perte de puissance moteur',
        'Consommation excessive de carburant',
        'Bruit anormal du bloc moteur',
        'Fumée excessive à l\'échappement',
        'Défaillance du système EGR',
        'Problème de démarrage à froid',

        # Transmission
        'Problème de boîte de vitesses',
        'Synchroniseur usé',
        'Embrayage hydraulique défectueux',
        'Fuite d\'huile de transmission',
        'Arbre de transmission endommagé',
        'Différentiel bloqué',
        'Bruit anormal de la transmission',

        # Système pneumatique
        'Défaillance du système pneumatique',
        'Compresseur d\'air défectueux',
        'Fuite d\'air dans le circuit',
        'Valve pneumatique bloquée',
        'Sécheur d\'air défaillant',
        'Pression d\'air insuffisante',

        # Freinage
        'Défaillance du système de freinage',
        'Usure des tambours de frein',
        'Défaillance du frein moteur',
        'Fuite du circuit de freinage',
        'Réglage incorrect des freins',
        'Défaillance du ralentisseur',

        # Direction et suspension
        'Problème de direction assistée',
        'Problème de suspension renforcée',
        'Lames de ressort cassées',
        'Amortisseurs défectueux',
        'Barre stabilisatrice endommagée',
        'Coussinets de suspension usés',

        # Hydraulique
        'Fuite du circuit hydraulique',
        'Pompe hydraulique défaillante',
        'Vérin hydraulique endommagé',
        'Pression hydraulique insuffisante',
        'Soupape de décharge bloquée',

        # Électrique
        'Défaillance du système électrique',
        'Alternateur haute capacité défectueux',
        'Problème de démarreur renforcé',
        'Court-circuit dans le faisceau',
        'Défaillance du tableau de bord',
        'Problème de communication CAN'
    ],

    'Engin chenillé': [
        # Système de chenilles
        'Défaillance du système de chenilles',
        'Patins de chenille endommagés',
        'Tension incorrecte des chenilles',
        'Barbotin usé ou endommagé',
        'Galets de roulement défectueux',
        'Roue tendeuse bloquée',
        'Désalignement des chenilles',
        'Bruit anormal du train de roulement',
        'Usure excessive des maillons',

        # Moteur
        'Défaillance du moteur diesel lourd',
        'Surchauffe du bloc moteur blindé',
        'Problème d\'alimentation carburant haute pression',
        'Défaillance du système de refroidissement renforcé',
        'Fuite d\'huile moteur haute performance',
        'Problème de démarrage en conditions extrêmes',
        'Défaillance du turbocompresseur blindé',
        'Perte de puissance moteur',

        # Transmission blindée
        'Problème de transmission blindée',
        'Défaillance du convertisseur de couple',
        'Surchauffe de la boîte de transfert',
        'Fuite d\'huile de transmission',
        'Défaillance des embrayages multidisques',
        'Problème de changement de rapport',
        'Bruit anormal de la transmission',

        # Direction
        'Problème de direction différentielle',
        'Défaillance du système de freinage directionnel',
        'Fuite du circuit hydraulique de direction',
        'Défaillance des leviers de direction',
        'Problème de pivotement sur place',

        # Hydraulique
        'Défaillance du système hydraulique',
        'Pompe hydraulique principale défectueuse',
        'Fuite des vérins hydrauliques',
        'Surchauffe du fluide hydraulique',
        'Pression hydraulique insuffisante',
        'Contamination du circuit hydraulique',

        # Électrique et électronique
        'Défaillance du système électrique blindé',
        'Problème de batterie haute capacité',
        'Court-circuit dans le système électrique protégé',
        'Défaillance du système de communication interne',
        'Problème d\'alimentation des systèmes électroniques',
        'Défaillance des capteurs de position',

        # Refroidissement
        'Problème de refroidissement',
        'Radiateur blindé obstrué',
        'Pompe à eau défectueuse',
        'Ventilateur de refroidissement endommagé',
        'Thermostat bloqué',
        'Fuite du circuit de refroidissement'
    ],

    'SA': [
        # Système de recul
        'Défaillance du système de recul hydraulique',
        'Vérins de recul endommagés',
        'Fuite du circuit hydraulique de recul',
        'Amortisseurs de recul défectueux',
        'Récupérateurs hydrauliques défaillants',
        'Problème de synchronisation du recul',

        # Système d\'élévation
        'Problème du mécanisme d\'élévation',
        'Défaillance des vérins d\'élévation',
        'Fuite hydraulique du système d\'élévation',
        'Blocage du mécanisme d\'élévation',
        'Usure des engrenages d\'élévation',
        'Problème de verrouillage en position',

        # Stabilisation
        'Défaillance du système de stabilisation',
        'Vérins stabilisateurs endommagés',
        'Capteurs de niveau défectueux',
        'Problème de verrouillage de position',
        'Défaillance des béquilles hydrauliques',
        'Instabilité en position de tir',

        # Pointage
        'Défaillance du système de pointage',
        'Problème de précision du pointage',
        'Défaillance des moteurs de pointage',
        'Blocage du système de visée',
        'Défaillance des encodeurs de position',
        'Problème de calibration du pointage',

        # Rotation
        'Défaillance du système de rotation',
        'Problème de la couronne d\'orientation',
        'Moteur de rotation défectueux',
        'Blocage du système de rotation',
        'Bruit anormal lors de la rotation',
        'Usure des roulements de rotation',

        # Électrique et électronique
        'Problème de circuit électrique spécialisé',
        'Défaillance du système de contrôle de tir',
        'Problème d\'alimentation des systèmes électroniques',
        'Défaillance des calculateurs balistiques',
        'Court-circuit dans le système électrique',
        'Défaillance des capteurs de position',

        # Hydraulique
        'Défaillance de la centrale hydraulique',
        'Fuite du circuit hydraulique principal',
        'Surchauffe du fluide hydraulique',
        'Contamination du circuit hydraulique',
        'Pression hydraulique insuffisante',
        'Défaillance des distributeurs hydrauliques',

        # Mécanique générale
        'Problème de culasse mobile',
        'Défaillance du système de chargement',
        'Usure des joints d\'étanchéité',
        'Défaillance du système de refroidissement',
        'Problème de verrouillage de sécurité'
    ]
}

@app.context_processor
def inject_now():
    return {'now': datetime.now()}

def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        # Pour l'instant, accepte n'importe quel login/mot de passe
        if username and password:
            session['logged_in'] = True
            session['username'] = username
            flash('Connexion réussie !', 'success')
            return redirect(url_for('index'))
        else:
            flash('Veuillez remplir tous les champs', 'danger')

    return render_template('login.html')

@app.route('/login_qr', methods=['POST'])
def login_qr():
    # Récupérer les données du code QR
    data = request.get_json()
    qr_data = data.get('qr_data', '')

    # Code QR unique autorisé pour l'utilisateur redouane
    # Ce code est spécifique et ne doit pas être modifié
    authorized_qr_code = "redouane_FAR_auth_7a9b3c2d1e"

    # Vérifier si le QR code correspond exactement au code autorisé
    if qr_data and qr_data == authorized_qr_code:
        # Authentifier en tant que redouane
        username = "redouane"

        # Initialiser la session avec l'utilisateur redouane
        session['logged_in'] = True
        session['username'] = username

        # Enregistrer dans les logs pour le suivi
        app.logger.info(f"Connexion réussie via QR code pour l'utilisateur {username}")

        return jsonify({
            'success': True,
            'message': 'Authentification réussie'
        })
    else:
        # Rejeter tout autre QR code
        app.logger.warning(f"Tentative de connexion avec QR code non autorisé: {qr_data}")

        return jsonify({
            'success': False,
            'message': 'Code QR non autorisé. Veuillez utiliser uniquement le QR code officiel.'
        })



@app.route('/login_facial', methods=['POST'])
def login_facial():
    # Récupérer les données de reconnaissance faciale
    data = request.get_json()
    facial_data = data.get('facial_data', '')

    # Pour cette implémentation, nous acceptons n'importe quel visage
    # Dans une implémentation réelle, vous vérifieriez les données biométriques

    if facial_data:
        # Authentifier en tant que "utilisateur X"
        username = "utilisateur X"

        # Initialiser la session
        session['logged_in'] = True
        session['username'] = username

        return jsonify({
            'success': True,
            'message': 'Authentification faciale réussie'
        })
    else:
        # Rejeter si aucune donnée faciale n'est fournie
        return jsonify({
            'success': False,
            'message': 'Données faciales manquantes'
        })

@app.route('/logout')
def logout():
    session.clear()
    flash('Vous avez été déconnecté', 'info')
    return redirect(url_for('login'))

@app.route('/')
@login_required
def index():
    # Récupérer le paramètre de recherche s'il existe
    search_matricule = request.args.get('search_matricule', '')

    # Filtrer les véhicules si un matricule est spécifié
    if (search_matricule):
        vehicules_query = Vehicule.query.filter(Vehicule.matricule.like(f"%{search_matricule}%")).order_by(Vehicule.date_panne.asc()).all()
        if vehicules_query:
            # Si des véhicules sont trouvés, afficher un message indiquant le nombre de pannes trouvées
            pannes_count = len(vehicules_query)
            if pannes_count == 1:
                flash(f'1 panne trouvée pour le matricule "{search_matricule}"', 'success')
            else:
                flash(f'{pannes_count} pannes trouvées pour le matricule "{search_matricule}"', 'success')
        else:
            flash(f'Aucun véhicule trouvé avec le matricule "{search_matricule}"', 'warning')
            vehicules_query = Vehicule.query.order_by(Vehicule.date_panne.asc()).all()  # Afficher tous les véhicules si aucun résultat
    else:
        vehicules_query = Vehicule.query.order_by(Vehicule.date_panne.asc()).all()

    # Convertir les véhicules en dictionnaires et trier par GAR
    vehicules = [v.to_dict() for v in vehicules_query]
    
    # Fonction pour extraire et trier correctement les numéros de GAR
    def get_gar_number(vehicule):
        if not vehicule['unite']:
            return (999, '')  # Mettre les véhicules sans GAR à la fin
        
        import re
        # Recherche du motif 1GAR, 2GAR, etc. (insensible à la casse)
        match = re.search(r'(\d+)GAR', vehicule['unite'], re.IGNORECASE)
        if match:
            # Retourne un tuple (numéro, texte) pour un tri correct
            return (int(match.group(1)), vehicule['unite'])
        return (999, vehicule['unite'])  # Mettre les autres unités à la fin
    
    # Trier les véhicules par numéro de GAR (tri naturel)
    vehicules.sort(key=get_gar_number)

    return render_template('index.html', vehicules=vehicules, search_matricule=search_matricule)

@app.route('/ajouter', methods=['GET', 'POST'])
@login_required
def ajouter():
    if request.method == 'POST':
        # Récupération des données du formulaire
        type_vehicule = request.form.get('type_vehicule')
        marque = request.form.get('marque')
        matricule = request.form.get('modele')  # Le champ du formulaire est toujours 'modele'
        type_panne = request.form.get('type_panne')
        date_panne = request.form.get('date_panne')
        description = request.form.get('description', '')
        unite = request.form.get('unite')
        statut = request.form.get('statut', 'En panne')

        # Validation des données
        if not all([type_vehicule, marque, matricule, type_panne, date_panne, statut]):
            flash('Tous les champs obligatoires doivent être remplis.', 'danger')
            return redirect(url_for('ajouter'))

        # Vérifier si le matricule existe dans la table vehicule_gar
        vehicule_gar = VehiculeGAR.query.filter_by(matricule=matricule).first()
        if not vehicule_gar:
            flash(f'Erreur : Le matricule "{matricule}" n\'existe pas dans la liste des véhicules GAR.', 'danger')
            return redirect(url_for('ajouter'))

        # Vérifier si le type de véhicule correspond
        if vehicule_gar.type_vehicule != type_vehicule:
            flash(f'Erreur : Le matricule "{matricule}" est attribué à un véhicule de type "{vehicule_gar.type_vehicule}". Impossible de l\'enregistrer comme "{type_vehicule}".', 'danger')
            return redirect(url_for('ajouter'))

        # Vérifier si "Autre (précisez)" est sélectionné et qu'un type de panne personnalisé est fourni
        if type_panne == 'autre':
            custom_panne = request.form.get('custom_panne', '').strip()
            if not custom_panne:
                flash('Veuillez spécifier le type de panne personnalisé.', 'danger')
                return redirect(url_for('ajouter'))
            type_panne = custom_panne
        
        # Vérifier si le même type de panne existe déjà pour ce véhicule
        panne_existante = Vehicule.query.filter_by(matricule=matricule, type_panne=type_panne).first()
        if panne_existante:
            flash(f'Erreur : Une panne de type "{type_panne}" est déjà enregistrée pour le véhicule avec matricule "{matricule}".', 'danger')
            return redirect(url_for('ajouter'))

        # Utiliser les informations du véhicule GAR
        type_vehicule = vehicule_gar.type_vehicule
        marque = vehicule_gar.marque
        unite = vehicule_gar.unite

        # Création du nouveau véhicule
        nouveau_vehicule = Vehicule(
            type_vehicule=type_vehicule,
            marque=marque,
            matricule=matricule,
            type_panne=type_panne,
            date_panne=datetime.strptime(date_panne, '%Y-%m-%d').date(),
            description=description,
            statut=statut,
            unite=unite
        )

        # Ajout à la base de données
        db.session.add(nouveau_vehicule)
        db.session.commit()

        # Créer une entrée dans l'historique pour l'état initial avec la date de panne
        try:
            # Utiliser la date de panne comme date de changement pour l'entrée initiale
            date_panne_obj = datetime.strptime(date_panne, '%Y-%m-%d').date()

            historique = VehiculeHistorique(
                vehicule_id=nouveau_vehicule.id,
                statut='En panne',
                type_panne=nouveau_vehicule.type_panne,
                date_panne=nouveau_vehicule.date_panne,
                date_changement=datetime.combine(date_panne_obj, datetime.now().time()),
                description=nouveau_vehicule.description if nouveau_vehicule.description else f'Véhicule ajouté avec panne : {nouveau_vehicule.type_panne}'
            )
            db.session.add(historique)
            db.session.commit()
            print(f"Historique créé pour le véhicule {nouveau_vehicule.id}")
        except Exception as e:
            print(f"Erreur lors de la création de l'historique: {e}")

        flash(f'Nouvelle panne ajoutée avec succès pour le véhicule avec matricule "{matricule}" !', 'success')
        return redirect(url_for('index'))

    return render_template('ajouter.html', types_vehicules=TYPES_VEHICULES, types_pannes=TYPES_PANNES)

@app.route('/modifier/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier(id):
    vehicule = Vehicule.query.get_or_404(id)

    if request.method == 'POST':
        # Récupérer l'ancien statut avant la modification
        ancien_statut = vehicule.statut

        # Récupérer le nouveau statut et la date de modification
        nouveau_statut = request.form.get('statut')
        date_modification_str = request.form.get('date_modification')

        # Validation des données
        if not all([nouveau_statut, date_modification_str]):
            flash('Tous les champs obligatoires doivent être remplis.', 'danger')
            return redirect(url_for('modifier', id=id))

        # Convertir la date de modification en objet date
        try:
            date_modification = datetime.strptime(date_modification_str, '%Y-%m-%d').date()
        except ValueError:
            flash('Format de date invalide.', 'danger')
            return redirect(url_for('modifier', id=id))

        # Mettre à jour uniquement le statut du véhicule
        vehicule.statut = nouveau_statut

        # Vérifier si le statut a changé
        if ancien_statut != nouveau_statut:
            try:
                # Créer une entrée dans l'historique avec les détails de la panne et la date de modification saisie
                historique = VehiculeHistorique(
                    vehicule_id=vehicule.id,
                    statut=nouveau_statut,
                    type_panne=vehicule.type_panne,
                    date_panne=vehicule.date_panne,
                    date_changement=datetime.combine(date_modification, datetime.now().time()),  # Utiliser la date saisie avec l'heure actuelle
                    description=request.form.get('description', '') if request.form.get('description', '') else f'Changement de statut : {nouveau_statut}'
                )
                db.session.add(historique)
                db.session.commit()
                print(f"Historique créé pour le changement de statut du véhicule {vehicule.id}")
            except Exception as e:
                print(f"Erreur lors de la création de l'historique: {e}")
                # Ne pas bloquer la modification du véhicule si l'historique échoue

        # Mise à jour dans la base de données
        db.session.commit()

        # Message de succès adapté au type de statut
        status_messages = {
            'Réparé': f'Véhicule marqué comme réparé le {date_modification.strftime("%d/%m/%Y")} !',
            'Indisponible': f'Véhicule marqué comme indisponible le {date_modification.strftime("%d/%m/%Y")} !',
            'En réparation': f'Véhicule mis en réparation le {date_modification.strftime("%d/%m/%Y")} !',
            'En panne': f'Statut du véhicule mis à jour le {date_modification.strftime("%d/%m/%Y")} !'
        }

        flash(status_messages.get(nouveau_statut, 'Statut du véhicule mis à jour avec succès !'), 'success')
        return redirect(url_for('index'))

    # Passer la date actuelle au template
    now = datetime.now()

    return render_template('modifier.html',
                          vehicule=vehicule,
                          types_vehicules=TYPES_VEHICULES,
                          types_pannes=TYPES_PANNES,
                          now=now)

@app.route('/supprimer/<int:id>', methods=['GET', 'POST'])
@login_required
def supprimer(id):
    try:
        # Récupérer le véhicule
        vehicule = Vehicule.query.get_or_404(id)

        # Supprimer directement le véhicule sans toucher à l'historique
        # Nous ignorerons la table vehicule_historique pour l'instant
        db.session.delete(vehicule)
        db.session.commit()

        flash('Véhicule supprimé avec succès', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"Erreur lors de la suppression du véhicule: {e}")
        flash(f'Erreur lors de la suppression du véhicule: {str(e)}', 'danger')

    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Statistiques de base
    total_vehicules = Vehicule.query.count()
    vehicules_vl = Vehicule.query.filter_by(type_vehicule='VL').count()
    vehicules_pl = Vehicule.query.filter_by(type_vehicule='PL').count()
    vehicules_sa = Vehicule.query.filter_by(type_vehicule='SA').count()
    vehicules_chenilles = Vehicule.query.filter_by(type_vehicule='Engin chenillé').count()

    # Statistiques par unité (GAR)
    gar_stats = {}
    gar_operational = {}
    gar_list = []  # Liste pour stocker les GAR dans l'ordre
    total_pannes = Vehicule.query.filter_by(statut='En panne').count()  # Total des pannes toutes unités confondues

    # Récupérer les unités distinctes dans la base de données
    unites_distinctes = db.session.query(VehiculeGAR.unite).distinct().all()
    
    # Trier les unités par numéro de GAR
    unites_triees = []
    for unite in unites_distinctes:
        unite_nom = unite[0]
        if unite_nom:
            # Extraire le numéro du GAR
            try:
                # Utiliser une expression régulière pour extraire le numéro
                import re
                match = re.search(r'(\d+)GAR', unite_nom)
                if match:
                    numero_gar = int(match.group(1))
                    unites_triees.append((numero_gar, unite_nom))
                else:
                    # Si ce n'est pas un GAR numéroté, mettre à la fin
                    unites_triees.append((999, unite_nom))
            except ValueError:
                # Si ce n'est pas un GAR numéroté, mettre à la fin
                unites_triees.append((999, unite_nom))
    
    # Trier par numéro de GAR (tri numérique)
    unites_triees.sort(key=lambda x: x[0])
    
    # Créer une liste ordonnée des GAR
    for numero_gar, unite_nom in unites_triees:
        # Compter tous les véhicules de cette unité dans VehiculeGAR
        total_unite = VehiculeGAR.query.filter_by(unite=unite_nom).count()
        if total_unite > 0:
            gar_stats[unite_nom] = total_unite

            # Compter uniquement les véhicules avec le statut 'En panne' pour cette unité
            vehicules_en_panne = Vehicule.query.filter_by(unite=unite_nom, statut='En panne').count()

            # Si aucun véhicule n'est en panne, le degré opérationnel est de 100%
            if vehicules_en_panne == 0:
                gar_operational[unite_nom] = 100.0
            # Si tous les véhicules sont en panne, le degré opérationnel est de 0%
            elif vehicules_en_panne == total_unite:
                gar_operational[unite_nom] = 0.0
            # Sinon, calculer le pourcentage de véhicules opérationnels
            else:
                vehicules_operationnels = total_unite - vehicules_en_panne
                pourcentage = (vehicules_operationnels / total_unite) * 100
                gar_operational[unite_nom] = round(pourcentage, 1)

            # Ne pas ajouter les GAR sans pannes à la liste
            if vehicules_en_panne > 0:
                # Calculer le pourcentage de pannes par rapport au total des pannes
                pourcentage_pannes = (vehicules_en_panne / total_pannes * 100) if total_pannes > 0 else 0
                
                gar_list.append({
                    'nom': unite_nom,
                    'total': total_unite,
                    'en_panne': vehicules_en_panne,
                    'operationnel': gar_operational[unite_nom],
                    'pourcentage_pannes': round(pourcentage_pannes, 1)  # Arrondi à 1 décimale
                })

            # Désactivé pour éviter les problèmes d'encodage
            # print(f"Debug - GAR {unite_nom}: Total={total_unite}, En panne={vehicules_en_panne}, Pourcentage={gar_operational[unite_nom]}%")  # Debug

    # Statistiques par marque
    marques_stats = {}
    for type_vehicule, marques in TYPES_VEHICULES.items():
        for marque in marques:
            count = Vehicule.query.filter_by(marque=marque).count()
            if count > 0:
                marques_stats[marque] = count

    # Statistiques par statut
    statuts_stats = {}
    statuts = ['En panne', 'Indisponible', 'En réparation', 'Réparé']
    for statut in statuts:
        count = Vehicule.query.filter_by(statut=statut).count()
        if count > 0:
            statuts_stats[statut] = count

    # Debug - Afficher les données avant de les envoyer au template
    print("Debug - GAR Stats:", gar_stats)
    print("Debug - GAR Operational:", gar_operational)
    print("Debug - GAR List:", gar_list)

    return render_template('dashboard.html',
                         total_vehicules=total_vehicules,
                         vehicules_vl=vehicules_vl,
                         vehicules_pl=vehicules_pl,
                         vehicules_sa=vehicules_sa,
                         vehicules_chenilles=vehicules_chenilles,
                         gar_stats=gar_stats,
                         gar_operational=gar_operational,
                         gar_list=gar_list,  # Passer la liste triée au template
                         marques_stats=marques_stats,
                         statuts_stats=statuts_stats)

@app.route('/get_marques/<type_vehicule>')
@login_required
def get_marques(type_vehicule):
    return jsonify(TYPES_VEHICULES.get(type_vehicule, []))

@app.route('/get_pannes/<type_vehicule>')
@login_required
def get_pannes(type_vehicule):
    return jsonify(TYPES_PANNES.get(type_vehicule, []))

@app.route('/chatbot')
@login_required
def chatbot():
    return render_template('chatbot.html')

@app.route('/qr_codes')
@login_required
def qr_codes():
    return render_template('qr_codes.html')

@app.route('/redouane_qr')
def redouane_qr():
    # Cette route affiche le QR code spécifique pour l'authentification de redouane
    # Ce code est unique et ne doit pas être modifié
    qr_data = "redouane_FAR_auth_7a9b3c2d1e"
    return render_template('redouane_qr.html', qr_data=qr_data)

@app.route('/historique')
@login_required
def historique():
    # Récupérer le paramètre de recherche s'il existe
    search_matricule = request.args.get('search_matricule', '')

    vehicule = None
    historique = None

    if search_matricule:
        # Récupérer tous les véhicules avec ce matricule (peut représenter plusieurs pannes)
        vehicules = Vehicule.query.filter_by(matricule=search_matricule).all()

        if vehicules:
            # Utiliser le premier véhicule pour les informations générales
            vehicule = vehicules[0]

            # Récupérer l'historique complet pour tous les véhicules avec ce matricule
            historique_details = []

            # Dictionnaire pour regrouper les entrées par type de panne et date de panne
            pannes_dict = {}

            # Récupérer toutes les pannes et leurs dates
            for v in vehicules:
                # Clé unique pour chaque panne: combinaison du type de panne et de la date de panne
                panne_key = f"{v.type_panne}_{v.date_panne.strftime('%Y-%m-%d')}"

                if panne_key not in pannes_dict:
                    pannes_dict[panne_key] = {
                        'vehicule_id': v.id,
                        'type_panne': v.type_panne,
                        'date_panne': v.date_panne,
                        'entries': []
                    }

            # Trier les pannes par date (de la plus ancienne à la plus récente)
            sorted_pannes = sorted(pannes_dict.values(), key=lambda x: x['date_panne'])

            # Pour chaque panne, récupérer son historique de changements de statut
            for panne in sorted_pannes:
                # Récupérer l'historique pour cette panne spécifique
                historique_entries = VehiculeHistorique.query\
                    .filter_by(vehicule_id=panne['vehicule_id'], type_panne=panne['type_panne'])\
                    .order_by(VehiculeHistorique.date_changement)\
                    .all()

                # Si aucune entrée n'est trouvée avec le type de panne spécifié, essayer sans filtre de type de panne
                # (pour la compatibilité avec les anciennes entrées)
                if not historique_entries:
                    historique_entries = VehiculeHistorique.query\
                        .filter_by(vehicule_id=panne['vehicule_id'])\
                        .order_by(VehiculeHistorique.date_changement)\
                        .all()

                # Ajouter les entrées d'historique
                for entry in historique_entries:
                    # Vérifier si l'entrée correspond à cette panne
                    entry_type_panne = entry.type_panne or panne['type_panne']
                    if entry_type_panne == panne['type_panne']:
                        historique_details.append({
                            'date_changement': entry.date_changement,
                            'date_panne': entry.date_panne or panne['date_panne'],
                            'type_panne': entry_type_panne,
                            'statut': entry.statut,
                            'description': entry.description,
                            'panne_date': panne['date_panne']  # Pour le tri par panne
                        })

            # Trier l'historique d'abord par date de panne, puis par date de changement
            historique_details.sort(key=lambda x: (x['panne_date'], x['date_changement']))

    return render_template('historique.html',
                           vehicule=vehicule,
                           historique=historique_details if vehicule else None,
                           search_matricule=search_matricule)

@app.route('/api/historique/<int:vehicule_id>')
@login_required
def api_historique(vehicule_id):
    vehicule = Vehicule.query.get(vehicule_id)
    if not vehicule:
        return jsonify({"error": "Véhicule non trouvé"}), 404

    # Récupérer l'historique du véhicule dans l'ordre chronologique (du plus ancien au plus récent)
    historique_query = VehiculeHistorique.query.filter_by(vehicule_id=vehicule_id).order_by(VehiculeHistorique.date_changement.asc()).all()

    # Convertir les objets en dictionnaires et s'assurer que le type de panne est inclus
    historique = []
    for h in historique_query:
        entry_dict = h.to_dict()
        # Si le type de panne n'est pas défini dans l'entrée d'historique, utiliser celui du véhicule
        if not entry_dict['type_panne']:
            entry_dict['type_panne'] = vehicule.type_panne
        historique.append(entry_dict)

    return jsonify({
        "vehicule": vehicule.to_dict(),
        "historique": historique
    })

@app.route('/chat', methods=['POST'])
@login_required
def chat():
    data = request.get_json()
    message = data.get('message', '').lower()

    # Analyse du message et génération de la réponse
    response = process_message(message)

    return jsonify({'response': response})

def process_message(message):
    # Recherche de véhicules
    if any(word in message for word in ['liste', 'afficher', 'voir', 'montrer']):
        vehicules_query = Vehicule.query.all()
        vehicules = [v.to_dict() for v in vehicules_query]

        if not vehicules:
            return "Il n'y a aucun véhicule enregistré dans le système."

        response = "Voici la liste des véhicules :<br><br>"
        for v in vehicules:
            response += f"""
            <div class="vehicle-card">
                <strong>Type:</strong> {v['type_vehicule']}<br>
                <strong>Marque:</strong> {v['marque']}<br>
                <strong>Matricule:</strong> {v['matricule']}<br>
                <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                <strong>Type de panne:</strong> {v['type_panne']}<br>
                <strong>Date de panne:</strong> {v['date_panne']}<br>
                <strong>Statut:</strong> {v['statut']}
            </div>
            """
        return response

    # Comptage des véhicules par statut
    elif any(word in message for word in ['combien', 'nombre', 'total']):
        # Recherche par statut
        statuts = ['en panne', 'indisponible', 'en réparation', 'réparé']
        for statut in statuts:
            if statut in message.lower():
                # Normaliser le statut (première lettre en majuscule)
                statut_norm = statut.capitalize()
                if statut == 'en panne':
                    statut_norm = 'En panne'
                elif statut == 'en réparation':
                    statut_norm = 'En réparation'

                count = Vehicule.query.filter_by(statut=statut_norm).count()
                if count == 0:
                    return f"Il n'y a actuellement aucun véhicule {statut}."
                elif count == 1:
                    return f"Il y a actuellement 1 véhicule {statut}."
                else:
                    return f"Il y a actuellement {count} véhicules {statut}."

        # Si aucun statut spécifique n'est mentionné
        if 'panne' in message:
            en_panne = Vehicule.query.filter_by(statut='En panne').count()
            if en_panne == 0:
                return "Il n'y a actuellement aucun véhicule en panne."
            elif en_panne == 1:
                return "Il y a actuellement 1 véhicule en panne."
            else:
                return f"Il y a actuellement {en_panne} véhicules en panne."
        else:
            total = Vehicule.query.count()
            return f"Il y a au total {total} véhicule(s) enregistré(s)."

    # Statistiques sur les marques
    elif 'marque' in message:
        total = Vehicule.query.count()
        if total == 0:
            return "Il n'y a aucun véhicule enregistré pour analyser les marques."

        marques = {}
        for type_v, marques_list in TYPES_VEHICULES.items():
            for marque in marques_list:
                count = Vehicule.query.filter_by(marque=marque).count()
                if count > 0:
                    marques[marque] = count

        response = "Voici la répartition par marque :<br><br>"
        for marque, count in sorted(marques.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total) * 100
            response += f"""
            <div class="vehicle-card">
                <strong>{marque}:</strong> {count} véhicule(s) ({percentage:.1f}%)
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: {percentage}%"></div>
                </div>
            </div>
            """
        return response

    # Recherche par unité (GAR) - Traitement unifié
    elif any(word in message for word in ['gar', 'unité', 'unite', 'unites', 'unités', 'groupement']):
        # Extraire le numéro de GAR du message
        import re

        # Ajouter du débogage
        print(f"Message GAR: {message}")

        # Essayer d'abord de trouver un motif comme "14 GAR" ou "unité 14"
        gar_match = re.search(r'(\d+)\s*(?:gar|unité|unite)', message.lower())
        if gar_match:
            print(f"Match 1: {gar_match.group(1)}")

        # Si ça ne marche pas, essayer de trouver un motif comme "véhicules du 14 GAR" ou "unité numéro 14"
        if not gar_match:
            gar_match = re.search(r'(?:gar|unité|unite).*?(\d+)', message.lower())
            if gar_match:
                print(f"Match 2: {gar_match.group(1)}")

        # Si ça ne marche toujours pas, chercher un numéro entre 10 et 30 (plage typique des GAR)
        if not gar_match and any(word in message.lower() for word in ['véhicule', 'vehicule', 'véhicules', 'vehicules']):
            num_match = re.findall(r'\b(\d+)\b', message.lower())
            for num in num_match:
                if 10 <= int(num) <= 30:
                    gar_match = re.match(r'(\d+)', num)
                    if gar_match:
                        print(f"Match 3: {gar_match.group(1)}")
                    break

        # Si on trouve un numéro de GAR spécifique
        if gar_match:
            gar_num = gar_match.group(1)
            print(f"GAR trouvé: {gar_num}")

            # Recherche flexible pour tenir compte des variations de format
            vehicules_query = Vehicule.query.filter(
                Vehicule.unite.like(f"%{gar_num}%") &
                Vehicule.unite.like("%GAR%")
            ).all()
            vehicules = [v.to_dict() for v in vehicules_query]

            if not vehicules:
                return f"Il n'y a aucun véhicule assigné à l'unité {gar_num} GAR."

            response = f"Voici les véhicules de l'unité {gar_num} GAR :<br><br>"
            for v in vehicules:
                response += f"""
                <div class="vehicle-card">
                    <strong>Type:</strong> {v['type_vehicule']}<br>
                    <strong>Marque:</strong> {v['marque']}<br>
                    <strong>Matricule:</strong> {v['matricule']}<br>
                    <strong>Type de panne:</strong> {v['type_panne']}<br>
                    <strong>Date de panne:</strong> {v['date_panne']}<br>
                    <strong>Statut:</strong> {v['statut']}
                </div>
                """
            return response

        # Si on demande les statistiques par GAR sans spécifier un numéro
        elif any(word in message.lower() for word in ['statistique', 'stats', 'répartition', 'repartition']):
            total = Vehicule.query.count()
            if total == 0:
                return "Il n'y a aucun véhicule enregistré pour analyser les unités."

            # Récupérer les unités distinctes
            unites_distinctes = db.session.query(Vehicule.unite).distinct().all()
            unites = {}

            for unite in unites_distinctes:
                unite_nom = unite[0]
                if unite_nom:
                    count = Vehicule.query.filter_by(unite=unite_nom).count()
                    if count > 0:
                        unites[unite_nom] = count
                else:
                    # Compter les véhicules sans unité assignée
                    count = Vehicule.query.filter(or_(Vehicule.unite == None, Vehicule.unite == '')).count()
                    if count > 0:
                        unites["Non assigné"] = count

            response = "Voici la répartition par unité (GAR) :<br><br>"
            for unite, count in sorted(unites.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total) * 100
                response += f"""
                <div class="vehicle-card">
                    <strong>{unite}:</strong> {count} véhicule(s) ({percentage:.1f}%)
                    <div class="progress">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: {percentage}%"></div>
                    </div>
                </div>
                """
            return response

        # Si on demande juste les GAR sans préciser
        else:
            # Récupérer les unités distinctes
            unites_distinctes = db.session.query(Vehicule.unite).distinct().all()
            unites = {}

            for unite in unites_distinctes:
                unite_nom = unite[0]
                if unite_nom:
                    count = Vehicule.query.filter_by(unite=unite_nom).count()
                    if count > 0:
                        unites[unite_nom] = count
                else:
                    # Compter les véhicules sans unité assignée
                    count = Vehicule.query.filter(or_(Vehicule.unite == None, Vehicule.unite == '')).count()
                    if count > 0:
                        unites["Non assigné"] = count

            if not unites:
                return "Il n'y a aucune unité (GAR) avec des véhicules assignés."

            response = "Voici la répartition des véhicules par unité (GAR) :<br><br>"
            for unite, count in sorted(unites.items(), key=lambda x: x[1], reverse=True):
                response += f"""
                <div class="vehicle-card">
                    <strong>{unite}:</strong> {count} véhicule(s)
                </div>
                """
            return response

    # Recherche par type de véhicule
    elif any(type_v.lower() in message for type_v in TYPES_VEHICULES.keys()):
        for type_v in TYPES_VEHICULES.keys():
            if type_v.lower() in message:
                vehicules_query = Vehicule.query.filter_by(type_vehicule=type_v).all()
                vehicules = [v.to_dict() for v in vehicules_query]

                if not vehicules:
                    return f"Il n'y a aucun véhicule de type {type_v} enregistré."

                response = f"Voici les véhicules de type {type_v} :<br><br>"
                for v in vehicules:
                    response += f"""
                    <div class="vehicle-card">
                        <strong>Marque:</strong> {v['marque']}<br>
                        <strong>Matricule:</strong> {v['matricule']}<br>
                        <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                        <strong>Type de panne:</strong> {v['type_panne']}<br>
                        <strong>Date de panne:</strong> {v['date_panne']}<br>
                        <strong>Statut:</strong> {v['statut']}
                    </div>
                    """
                return response

    # Cette section a été fusionnée avec la section "Recherche par unité (GAR) - Traitement unifié" ci-dessus

    # Recherche par statut
    elif any(statut.lower() in message.lower() for statut in ['en panne', 'indisponible', 'en réparation', 'réparé']):
        statuts = ['en panne', 'indisponible', 'en réparation', 'réparé']
        for statut in statuts:
            if statut in message.lower():
                # Normaliser le statut (première lettre en majuscule)
                statut_norm = statut.capitalize()
                if statut == 'en panne':
                    statut_norm = 'En panne'
                elif statut == 'en réparation':
                    statut_norm = 'En réparation'

                # Si le message contient des mots comme "liste", "afficher", etc., montrer les véhicules
                if any(word in message.lower() for word in ['liste', 'afficher', 'voir', 'montrer', 'montre', 'affiche', 'donne', 'quels', 'quelles', 'véhicule', 'vehicule', 'véhicules', 'vehicules']):
                    vehicules_query = Vehicule.query.filter_by(statut=statut_norm).all()
                    vehicules = [v.to_dict() for v in vehicules_query]

                    if not vehicules:
                        return f"Il n'y a aucun véhicule avec le statut '{statut_norm}'."

                    response = f"Voici les véhicules avec le statut '{statut_norm}' :<br><br>"
                    for v in vehicules:
                        response += f"""
                        <div class="vehicle-card">
                            <strong>Type:</strong> {v['type_vehicule']}<br>
                            <strong>Marque:</strong> {v['marque']}<br>
                            <strong>Matricule:</strong> {v['matricule']}<br>
                            <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                            <strong>Type de panne:</strong> {v['type_panne']}<br>
                            <strong>Date de panne:</strong> {v['date_panne']}
                        </div>
                        """
                    return response
                # Sinon, compter les véhicules avec ce statut
                else:
                    count = Vehicule.query.filter_by(statut=statut_norm).count()
                    if count == 0:
                        return f"Il n'y a actuellement aucun véhicule {statut}."
                    elif count == 1:
                        return f"Il y a actuellement 1 véhicule {statut}."
                    else:
                        return f"Il y a actuellement {count} véhicules {statut}."

    # Recherche par type de panne
    elif any(panne.lower() in message for panne in [p.lower() for type_pannes in TYPES_PANNES.values() for p in type_pannes]):
        for type_v, pannes_list in TYPES_PANNES.items():
            for panne in pannes_list:
                if panne.lower() in message.lower():
                    vehicules_query = Vehicule.query.filter_by(type_panne=panne).all()
                    vehicules = [v.to_dict() for v in vehicules_query]

                    if not vehicules:
                        return f"Il n'y a aucun véhicule avec une panne de type '{panne}'."

                    response = f"Voici les véhicules avec une panne de type '{panne}' :<br><br>"
                    for v in vehicules:
                        response += f"""
                        <div class="vehicle-card">
                            <strong>Type:</strong> {v['type_vehicule']}<br>
                            <strong>Marque:</strong> {v['marque']}<br>
                            <strong>Matricule:</strong> {v['matricule']}<br>
                            <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                            <strong>Date de panne:</strong> {v['date_panne']}<br>
                            <strong>Statut:</strong> {v['statut']}
                        </div>
                        """
                    return response

    # Recherche par matricule
    elif any(word in message.lower() for word in ['matricule', 'immatriculation', 'numéro', 'numero']):
        import re

        # Mots à exclure de la recherche de matricule
        mots_exclus = ['matricule', 'immatriculation', 'numéro', 'numero', 'du', 'le', 'la', 'les', 'des', 'un', 'une',
                      'recherche', 'cherche', 'trouve', 'montre', 'affiche', 'voir', 'vehicule', 'véhicule', 'historique']

        # Extraire tous les mots du message
        mots = re.findall(r'\b[A-Za-z0-9-]+\b', message)

        # Filtrer les mots exclus
        matricules_potentiels = [mot for mot in mots if mot.lower() not in mots_exclus]

        # Si nous avons des matricules potentiels
        if matricules_potentiels:
            # Prendre le premier matricule potentiel
            matricule = matricules_potentiels[0]

            # Afficher le matricule trouvé pour le débogage
            print(f"Matricule trouvé: {matricule}")

            # Vérifier si le matricule est présent dans le message et n'est pas juste un mot comme "matricule"
            if matricule.lower() not in mots_exclus:
                # Recherche du véhicule par matricule
                vehicule = Vehicule.query.filter(Vehicule.matricule.like(f"%{matricule}%")).first()

                if vehicule:
                    v = vehicule.to_dict()

                    # Récupérer l'historique réel depuis la table vehicule_historique
                    try:
                        # Récupérer l'historique depuis la base de données
                        historique_query = VehiculeHistorique.query.filter_by(vehicule_id=vehicule.id).order_by(VehiculeHistorique.date_changement).all()

                        # Convertir les objets en dictionnaires pour faciliter l'affichage
                        historique = []
                        for entry in historique_query:
                            historique.append({
                                "date": entry.date_changement.strftime('%Y-%m-%d'),
                                "statut": entry.statut,
                                "description": entry.description
                            })

                        print(f"Historique réel trouvé pour le véhicule {vehicule.id}: {len(historique)} entrées")

                        # Si aucun historique n'est trouvé, générer un historique simulé
                        if not historique:
                            print(f"Aucun historique trouvé pour le véhicule {vehicule.id}, génération d'un historique simulé")
                            if v['statut'] == 'En panne':
                                historique = [
                                    {"date": v['date_panne'], "statut": "En panne", "description": "Panne initiale signalée (simulé)"}
                                ]
                            elif v['statut'] == 'En réparation':
                                historique = [
                                    {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d'),
                                     "statut": "En panne", "description": "Panne initiale signalée (simulé)"},
                                    {"date": v['date_panne'], "statut": "En réparation", "description": "Début des réparations (simulé)"}
                                ]
                            elif v['statut'] == 'Réparé':
                                historique = [
                                    {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=14)).strftime('%Y-%m-%d'),
                                     "statut": "En panne", "description": "Panne initiale signalée (simulé)"},
                                    {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d'),
                                     "statut": "En réparation", "description": "Début des réparations (simulé)"},
                                    {"date": v['date_panne'], "statut": "Réparé", "description": "Réparations terminées (simulé)"}
                                ]
                            else:
                                historique = [
                                    {"date": v['date_panne'], "statut": v['statut'], "description": "État initial (simulé)"}
                                ]
                    except Exception as e:
                        print(f"Erreur lors de la récupération de l'historique: {e}")
                        # En cas d'erreur, générer un historique simulé
                        if v['statut'] == 'En panne':
                            historique = [
                                {"date": v['date_panne'], "statut": "En panne", "description": "Panne initiale signalée (simulé après erreur)"}
                            ]
                        elif v['statut'] == 'En réparation':
                            historique = [
                                {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d'),
                                 "statut": "En panne", "description": "Panne initiale signalée (simulé après erreur)"},
                                {"date": v['date_panne'], "statut": "En réparation", "description": "Début des réparations (simulé après erreur)"}
                            ]
                        elif v['statut'] == 'Réparé':
                            historique = [
                                {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=14)).strftime('%Y-%m-%d'),
                                 "statut": "En panne", "description": "Panne initiale signalée (simulé après erreur)"},
                                {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d'),
                                 "statut": "En réparation", "description": "Début des réparations (simulé après erreur)"},
                                {"date": v['date_panne'], "statut": "Réparé", "description": "Réparations terminées (simulé après erreur)"}
                            ]
                        else:
                            historique = [
                                {"date": v['date_panne'], "statut": v['statut'], "description": "État initial (simulé après erreur)"}
                            ]

                    # Construire la réponse avec l'état actuel et l'historique
                    response = f"""
                    <div class="vehicle-detail">
                        <h4>État actuel du véhicule matricule {v['matricule']}</h4>
                        <div class="vehicle-card">
                            <strong>Type:</strong> {v['type_vehicule']}<br>
                            <strong>Marque:</strong> {v['marque']}<br>
                            <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                            <strong>Type de panne:</strong> {v['type_panne']}<br>
                            <strong>Date de panne:</strong> {v['date_panne']}<br>
                            <strong>Statut actuel:</strong> <span class="badge bg-{'success' if v['statut'] == 'Réparé' else ('warning text-dark' if v['statut'] == 'Indisponible' else ('info' if v['statut'] == 'En réparation' else 'danger'))}">{v['statut']}</span><br>
                            <strong>Description:</strong> {v['description'] if v['description'] else 'Aucune description disponible'}
                        </div>

                        <h4 class="mt-4">Historique du véhicule</h4>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Statut</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                    """

                    # Ajouter chaque entrée d'historique
                    for entry in historique:
                        status_class = 'success' if entry["statut"] == 'Réparé' else ('warning' if entry["statut"] == 'Indisponible' else ('info' if entry["statut"] == 'En réparation' else 'danger'))
                        response += f"""
                                <tr>
                                    <td>{entry["date"]}</td>
                                    <td><span class="badge bg-{status_class}">{entry["statut"]}</span></td>
                                    <td>{entry["description"]}</td>
                                </tr>
                        """

                    response += """
                            </tbody>
                        </table>
                    </div>
                    """

                    return response
                else:
                    return f"Aucun véhicule avec le matricule '{matricule}' n'a été trouvé."

    # Réponse par défaut
    return """Je peux vous aider à :
    - Voir la liste des véhicules
    - Compter les véhicules par statut (en panne, indisponible, en réparation, réparé)
    - Voir les statistiques par marque ou par unité (GAR)
    - Rechercher des véhicules par type, type de panne ou statut
    - Afficher les véhicules d'une unité spécifique
    - Rechercher un véhicule par matricule et voir son historique

    Exemples de questions :
    - "Combien y a-t-il de véhicules en panne ?"
    - "Montre-moi les véhicules indisponibles"
    - "Affiche les statistiques par GAR"
    - "Quels sont les véhicules du 13 GAR ?"
    - "Recherche le véhicule matricule ABC123"
    - "Montre-moi l'historique du véhicule XYZ789"

    Que souhaitez-vous savoir ?"""

# Routes pour la gestion des véhicules GAR
@app.route('/liste_vehicules_gar')
def liste_vehicules_gar():
    vehicules = VehiculeGAR.query.all()
    return render_template('liste_vehicules_gar.html', vehicules=vehicules)

@app.route('/ajouter_vehicule_gar', methods=['GET', 'POST'])
def ajouter_vehicule_gar():
    if request.method == 'POST':
        matricule = request.form.get('matricule')
        unite = request.form.get('unite')
        type_vehicule = request.form.get('type_vehicule')
        marque = request.form.get('marque')

        # Vérification si le matricule existe déjà
        if VehiculeGAR.query.filter_by(matricule=matricule).first():
            flash('Un véhicule avec ce matricule existe déjà.', 'danger')
            return redirect(url_for('ajouter_vehicule_gar'))

        nouveau_vehicule = VehiculeGAR(
            matricule=matricule,
            unite=unite,
            type_vehicule=type_vehicule,
            marque=marque
        )

        try:
            db.session.add(nouveau_vehicule)
            db.session.commit()
            flash('Véhicule ajouté avec succès!', 'success')
            return redirect(url_for('liste_vehicules_gar'))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de l\'ajout du véhicule: ' + str(e), 'danger')
            return redirect(url_for('ajouter_vehicule_gar'))

    return render_template('ajouter_vehicule_gar.html')

@app.route('/modifier_vehicule_gar/<int:id>', methods=['GET', 'POST'])
def modifier_vehicule_gar(id):
    vehicule = VehiculeGAR.query.get_or_404(id)
    
    if request.method == 'POST':
        vehicule.matricule = request.form.get('matricule')
        vehicule.unite = request.form.get('unite')
        vehicule.type_vehicule = request.form.get('type_vehicule')
        vehicule.marque = request.form.get('marque')
        
        try:
            db.session.commit()
            flash('Véhicule modifié avec succès!', 'success')
            return redirect(url_for('liste_vehicules_gar'))
        except:
            db.session.rollback()
            flash('Erreur lors de la modification du véhicule', 'danger')
            
    return render_template('modifier_vehicule_gar.html', vehicule=vehicule)

@app.route('/supprimer_vehicule_gar/<int:id>')
def supprimer_vehicule_gar(id):
    vehicule = VehiculeGAR.query.get_or_404(id)
    try:
        db.session.delete(vehicule)
        db.session.commit()
        flash('Véhicule supprimé avec succès!', 'success')
    except:
        db.session.rollback()
        flash('Erreur lors de la suppression du véhicule', 'danger')
    return redirect(url_for('liste_vehicules_gar'))

@app.route('/verifier_vehicule')
def verifier_vehicule():
    matricule = request.args.get('matricule')

    if not matricule:
        return jsonify({'success': False, 'message': 'Matricule non fourni'}), 400

    try:
        # Recherche exacte
        vehicule = VehiculeGAR.query.filter_by(matricule=matricule).first()
        
        if not vehicule:
            # Si pas de correspondance exacte, recherche avec LIKE
            vehicule = VehiculeGAR.query.filter(VehiculeGAR.matricule.like(f"%{matricule}%")).first()

        if vehicule:
            return jsonify({
                'success': True,
                'vehicule': {
                    'id': vehicule.id,
                    'matricule': vehicule.matricule,
                    'unite': vehicule.unite,
                    'type_vehicule': vehicule.type_vehicule,
                    'marque': vehicule.marque
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': f'Aucun véhicule trouvé avec le matricule "{matricule}".'
            })

    except Exception as e:
        print(f"Erreur lors de la vérification du véhicule : {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erreur interne lors de la vérification du véhicule : {str(e)}'
        }), 500

@app.route('/initialiser_vehicules_gar')
@login_required
def initialiser_vehicules_gar():
    try:
        # Supprimer tous les véhicules GAR existants
        VehiculeGAR.query.delete()
        
        # Liste des marques pour chaque type de véhicule
        marques_vl = ['Toyota', 'Atlas 3', 'Atlas 4', 'Atlas 5', 'VLRA', 'Hummer', 'Nissan']
        marques_pl = ['JBD', 'Kaiser', 'Renault']
        
        # Compteur de matricule global
        matricule_base = 1000
        
        # Pour chaque GAR (de 1 à 26)
        for gar_num in range(1, 27):
            gar = f"{gar_num}GAR"
            
            # Ajouter 10 VL
            for i in range(10):
                matricule = str(matricule_base + i)  # Format: 1000, 1001, 1002, etc.
                marque = marques_vl[i % len(marques_vl)]
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='VL',
                    marque=marque
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour les PL
            matricule_base += 10
            
            # Ajouter 5 PL
            for i in range(5):
                matricule = str(matricule_base + i)  # Format: 1010, 1011, 1012, etc.
                marque = marques_pl[i % len(marques_pl)]
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='PL',
                    marque=marque
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour le prochain GAR
            matricule_base += 5
        
        db.session.commit()
        flash('Véhicules GAR initialisés avec succès !', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'initialisation des véhicules GAR : {str(e)}', 'danger')
    
    return redirect(url_for('liste_vehicules_gar'))

@app.route('/ajouter_engins_chenilles')
@login_required
def ajouter_engins_chenilles():
    try:
        # Compteur de matricule global
        matricule_base = 1390
        
        # Pour chaque GAR (de 1 à 26)
        for gar_num in range(1, 27):
            gar = f"{gar_num}GAR"
            
            # Ajouter 6 engins chenillés M109
            for i in range(6):
                matricule = str(matricule_base + i)
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='Engin chenillé',
                    marque='M109 155mm'
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour le prochain GAR
            matricule_base += 6
        
        db.session.commit()
        flash('Engins chenillés ajoutés avec succès !', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout des engins chenillés : {str(e)}', 'danger')
    
    return redirect(url_for('liste_vehicules_gar'))

@app.route('/ajouter_vehicules_speciaux')
@login_required
def ajouter_vehicules_speciaux():
    try:
        # Compteur de matricule global (commence après les M109)
        matricule_base = 1390 + (26 * 6)  # 26 GAR * 6 M109 par GAR
        
        # Pour chaque GAR (de 1 à 26)
        for gar_num in range(1, 27):
            gar = f"{gar_num}GAR"
            
            # Ajouter 3 M110
            for i in range(3):
                matricule = str(matricule_base + i)
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='Engin chenillé',
                    marque='M110 203mm'
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour les SA
            matricule_base += 3
            
            # Ajouter 3 SA
            for i in range(3):
                matricule = str(matricule_base + i)
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='SA',
                    marque='PULS'
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour le prochain GAR
            matricule_base += 3
        
        db.session.commit()
        flash('Véhicules spéciaux (M110 et SA) ajoutés avec succès !', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout des véhicules spéciaux : {str(e)}', 'danger')
    
    return redirect(url_for('liste_vehicules_gar'))

@app.route('/get_unites')
def get_unites():
    try:
        # Récupérer toutes les unités distinctes de la table vehicule_gar
        unites = db.session.query(VehiculeGAR.unite).distinct().all()
        
        # Convertir le résultat en liste et extraire les numéros de GAR
        unites_triees = []
        for unite in unites:
            if unite[0]:
                # Extraire le numéro du GAR
                import re
                match = re.search(r'(\d+)GAR', unite[0])
                if match:
                    numero_gar = int(match.group(1))
                    unites_triees.append((numero_gar, unite[0]))
                else:
                    # Si ce n'est pas un GAR numéroté, mettre à la fin
                    unites_triees.append((999, unite[0]))
        
        # Trier par numéro de GAR
        unites_triees.sort(key=lambda x: x[0])
        
        # Extraire uniquement les noms des unités dans l'ordre
        unites_list = [unite[1] for unite in unites_triees]
        
        return jsonify(unites_list)
    except Exception as e:
        print(f"Erreur lors de la récupération des unités: {str(e)}")
        return jsonify([]), 500

@app.route('/generer_donnees_test')
def generer_donnees_test():
    try:
        # Supprimer les données existantes
        Vehicule.query.delete()
        VehiculeHistorique.query.delete()
        db.session.commit()

        # Récupérer tous les véhicules de la table vehicule_gar
        vehicules_gar = VehiculeGAR.query.all()
        
        if not vehicules_gar:
            flash('Aucun véhicule trouvé dans la table vehicule_gar. Veuillez d\'abord initialiser les véhicules GAR.', 'warning')
            return redirect(url_for('index'))

        # Types de pannes par type de véhicule
        types_pannes = {
            'VL': [
                'Fuite d\'huile moteur', 'Surchauffe moteur', 'Défaillance du système d\'injection',
                'Problème de démarrage', 'Courroie de distribution cassée', 'Joint de culasse défectueux',
                'Perte de puissance moteur', 'Consommation excessive d\'huile', 'Bruit anormal du moteur',
                'Fumée blanche à l\'échappement', 'Fumée noire à l\'échappement', 'Embrayage défectueux',
                'Problème de transmission', 'Boîte de vitesses bloquée', 'Difficulté à passer les vitesses',
                'Bruit anormal de la transmission', 'Fuite d\'huile de transmission',
                'Usure prématurée de l\'embrayage', 'Défaillance du système de freinage',
                'Usure des plaquettes de frein', 'Disques de frein voilés', 'Fuite de liquide de frein',
                'ABS défectueux', 'Frein à main défaillant', 'Problème de direction assistée',
                'Amortisseurs usés', 'Rotules de suspension défectueuses', 'Barre stabilisatrice cassée',
                'Craquements dans la suspension', 'Alignement des roues incorrect', 'Batterie déchargée',
                'Alternateur défectueux', 'Démarreur défaillant', 'Court-circuit électrique',
                'Problème de faisceau électrique', 'Défaillance des phares tactiques',
                'Système radio défectueux', 'Pompe à carburant défaillante'
            ],
            'PL': [
                'Fuite d\'huile moteur', 'Surchauffe moteur', 'Défaillance du système d\'injection',
                'Problème de démarrage', 'Courroie de distribution cassée', 'Joint de culasse défectueux',
                'Perte de puissance moteur', 'Consommation excessive d\'huile', 'Bruit anormal du moteur',
                'Fumée blanche à l\'échappement', 'Fumée noire à l\'échappement', 'Embrayage défectueux',
                'Problème de transmission', 'Boîte de vitesses bloquée', 'Difficulté à passer les vitesses',
                'Bruit anormal de la transmission', 'Fuite d\'huile de transmission',
                'Usure prématurée de l\'embrayage', 'Défaillance du système de freinage',
                'Usure des plaquettes de frein', 'Disques de frein voilés', 'Fuite de liquide de frein',
                'ABS défectueux', 'Frein à main défaillant', 'Problème de direction assistée',
                'Amortisseurs usés', 'Rotules de suspension défectueuses', 'Barre stabilisatrice cassée',
                'Craquements dans la suspension', 'Alignement des roues incorrect', 'Batterie déchargée',
                'Alternateur défectueux', 'Démarreur défaillant', 'Court-circuit électrique',
                'Problème de faisceau électrique', 'Défaillance des phares tactiques',
                'Système radio défectueux', 'Pompe à carburant défaillante'
            ],
            'Engin chenillé': [
                'Fuite d\'huile moteur', 'Surchauffe moteur', 'Défaillance du système d\'injection',
                'Problème de démarrage', 'Courroie de distribution cassée', 'Joint de culasse défectueux',
                'Perte de puissance moteur', 'Consommation excessive d\'huile', 'Bruit anormal du moteur',
                'Fumée blanche à l\'échappement', 'Fumée noire à l\'échappement', 'Embrayage défectueux',
                'Problème de transmission', 'Boîte de vitesses bloquée', 'Difficulté à passer les vitesses',
                'Bruit anormal de la transmission', 'Fuite d\'huile de transmission',
                'Usure prématurée de l\'embrayage', 'Défaillance du système de freinage',
                'Usure des plaquettes de frein', 'Disques de frein voilés', 'Fuite de liquide de frein',
                'ABS défectueux', 'Frein à main défaillant', 'Problème de direction assistée',
                'Amortisseurs usés', 'Rotules de suspension défectueuses', 'Barre stabilisatrice cassée',
                'Craquements dans la suspension', 'Alignement des roues incorrect', 'Batterie déchargée',
                'Alternateur défectueux', 'Démarreur défaillant', 'Court-circuit électrique',
                'Problème de faisceau électrique', 'Défaillance des phares tactiques',
                'Système radio défectueux', 'Pompe à carburant défaillante'
            ],
            'SA': [
                'Fuite d\'huile moteur', 'Surchauffe moteur', 'Défaillance du système d\'injection',
                'Problème de démarrage', 'Courroie de distribution cassée', 'Joint de culasse défectueux',
                'Perte de puissance moteur', 'Consommation excessive d\'huile', 'Bruit anormal du moteur',
                'Fumée blanche à l\'échappement', 'Fumée noire à l\'échappement', 'Embrayage défectueux',
                'Problème de transmission', 'Boîte de vitesses bloquée', 'Difficulté à passer les vitesses',
                'Bruit anormal de la transmission', 'Fuite d\'huile de transmission',
                'Usure prématurée de l\'embrayage', 'Défaillance du système de freinage',
                'Usure des plaquettes de frein', 'Disques de frein voilés', 'Fuite de liquide de frein',
                'ABS défectueux', 'Frein à main défaillant', 'Problème de direction assistée',
                'Amortisseurs usés', 'Rotules de suspension défectueuses', 'Barre stabilisatrice cassée',
                'Craquements dans la suspension', 'Alignement des roues incorrect', 'Batterie déchargée',
                'Alternateur défectueux', 'Démarreur défaillant', 'Court-circuit électrique',
                'Problème de faisceau électrique', 'Défaillance des phares tactiques',
                'Système radio défectueux', 'Pompe à carburant défaillante'
            ]
        }

        # Statuts possibles
        statuts = ['En panne', 'Indisponible', 'En réparation', 'Réparé']

        # Sélectionner aléatoirement 55 véhicules de la table vehicule_gar
        vehicules_selectionnes = random.sample(vehicules_gar, min(55, len(vehicules_gar)))

        # Générer les pannes pour les véhicules sélectionnés
        for vehicule_gar in vehicules_selectionnes:
            type_panne = random.choice(types_pannes[vehicule_gar.type_vehicule])
            statut = random.choice(statuts)
            
            # Générer une date aléatoire dans les 30 derniers jours
            date_panne = datetime.now() - timedelta(days=random.randint(0, 30))
            
            # Créer le véhicule avec les informations de vehicule_gar
            vehicule = Vehicule(
                type_vehicule=vehicule_gar.type_vehicule,
                marque=vehicule_gar.marque,
                matricule=vehicule_gar.matricule,
                type_panne=type_panne,
                date_panne=date_panne.date(),
                description=f"Description de la panne pour {vehicule_gar.matricule}",
                statut=statut,
                unite=vehicule_gar.unite
            )
            db.session.add(vehicule)
            db.session.flush()  # Pour obtenir l'ID du véhicule

            # Créer l'historique
            historique = VehiculeHistorique(
                vehicule_id=vehicule.id,
                statut=statut,
                type_panne=type_panne,
                date_panne=date_panne.date(),
                description=f"État initial pour {vehicule_gar.matricule}"
            )
            db.session.add(historique)

        db.session.commit()
        flash(f'{len(vehicules_selectionnes)} pannes de test ont été générées avec succès!', 'success')
        return redirect(url_for('index'))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la génération des données de test: {str(e)}', 'danger')
        return redirect(url_for('index'))

# Routes pour la gestion des entretiens
@app.route('/entretiens')
@login_required
def entretiens():
    # Récupérer le paramètre de recherche s'il existe
    search_matricule = request.args.get('search_matricule', '')

    # Construire la requête de base
    query = Entretien.query.join(VehiculeGAR)

    # Filtrer par matricule si une recherche est effectuée
    if search_matricule:
        query = query.filter(VehiculeGAR.matricule.like(f"%{search_matricule}%"))
        
        # Récupérer les résultats
        entretiens = query.order_by(Entretien.date_entretien.desc()).all()
        
        if entretiens:
            # Message de succès avec le nombre d'entretiens trouvés
            count = len(entretiens)
            flash(f"{count} entretien{'s' if count > 1 else ''} trouvé{'s' if count > 1 else ''} pour le matricule \"{search_matricule}\"", 'success')
        else:
            # Message si aucun entretien n'est trouvé
            flash(f'Aucun entretien trouvé pour le matricule "{search_matricule}"', 'warning')
            # Charger tous les entretiens si aucun résultat
            entretiens = Entretien.query.order_by(Entretien.date_entretien.desc()).all()
    else:
        # Sans recherche, charger tous les entretiens
        entretiens = query.order_by(Entretien.date_entretien.desc()).all()

    # Charger la liste des véhicules pour le formulaire d'ajout
    vehicules = VehiculeGAR.query.all()

    return render_template('entretiens.html', 
                         entretiens=entretiens, 
                         vehicules=vehicules,
                         search_matricule=search_matricule)

@app.route('/entretiens/ajouter', methods=['POST'])
@login_required
def ajouter_entretien():
    if request.method == 'POST':
        try:
            # Récupération des données du formulaire
            vehicule_id = request.form.get('vehicule_id')
            type_entretien = request.form.get('type_entretien')
            if not type_entretien:
                type_entretien = 'Vidange'
            date_entretien = datetime.strptime(request.form.get('date_entretien'), '%Y-%m-%d')
            kilometrage = int(request.form.get('kilometrage'))
            kilometrage_prochain = int(request.form.get('kilometrage_prochain')) if request.form.get('kilometrage_prochain') else None
            description = request.form.get('description')
            # Récupérer toutes les pièces cochées et les joindre par des virgules
            pieces_remplacees_list = request.form.getlist('pieces_remplacees[]')
            pieces_remplacees = ', '.join(pieces_remplacees_list) if pieces_remplacees_list else None
            prochain_entretien = datetime.strptime(request.form.get('prochain_entretien'), '%Y-%m-%d') if request.form.get('prochain_entretien') else None
            statut = request.form.get('statut')

            # Création du nouvel entretien
            nouvel_entretien = Entretien(
                vehicule_id=vehicule_id,
                type_entretien=type_entretien,
                date_entretien=date_entretien,
                kilometrage=kilometrage,
                kilometrage_prochain=kilometrage_prochain,
                description=description,
                pieces_remplacees=pieces_remplacees,
                prochain_entretien=prochain_entretien,
                statut=statut
            )

            # Ajout à la base de données
            db.session.add(nouvel_entretien)
            db.session.commit()

            flash('Entretien ajouté avec succès !', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'ajout de l\'entretien : {str(e)}', 'danger')

    return redirect(url_for('entretiens'))

@app.route('/entretiens/modifier/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier_entretien(id):
    entretien = Entretien.query.get_or_404(id)
    vehicules = VehiculeGAR.query.all()

    if request.method == 'POST':
        try:
            # Mise à jour des données
            entretien.vehicule_id = request.form.get('vehicule_id')
            entretien.type_entretien = request.form.get('type_entretien')
            entretien.date_entretien = datetime.strptime(request.form.get('date_entretien'), '%Y-%m-%d')
            entretien.kilometrage = int(request.form.get('kilometrage'))
            entretien.kilometrage_prochain = int(request.form.get('kilometrage_prochain')) if request.form.get('kilometrage_prochain') else None
            entretien.description = request.form.get('description')
            # Récupérer toutes les pièces cochées et les joindre par des virgules
            pieces_remplacees_list = request.form.getlist('pieces_remplacees[]')
            entretien.pieces_remplacees = ', '.join(pieces_remplacees_list) if pieces_remplacees_list else None
            entretien.prochain_entretien = datetime.strptime(request.form.get('prochain_entretien'), '%Y-%m-%d') if request.form.get('prochain_entretien') else None
            entretien.statut = request.form.get('statut')

            # Mise à jour dans la base de données
            db.session.commit()

            flash('Entretien modifié avec succès !', 'success')
            return redirect(url_for('entretiens'))
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la modification de l\'entretien : {str(e)}', 'danger')

    return render_template('modifier_entretien.html', entretien=entretien, vehicules=vehicules)

@app.route('/mise_a_jour_kilometrage/<int:vehicule_id>', methods=['POST'])
@login_required
def mise_a_jour_kilometrage(vehicule_id):
    try:
        vehicule = VehiculeGAR.query.get_or_404(vehicule_id)
        nouveau_kilometrage = int(request.form.get('kilometrage'))
        
        # Mise à jour du kilométrage du véhicule
        vehicule.kilometrage = nouveau_kilometrage
        
        # Mise à jour du dernier kilométrage connu pour les entretiens
        dernier_entretien = Entretien.query.filter_by(vehicule_id=vehicule_id)\
            .order_by(Entretien.date_entretien.desc()).first()
            
        if dernier_entretien:
            dernier_entretien.kilometrage = nouveau_kilometrage
        
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/entretiens/supprimer/<int:id>', methods=['POST'])
@login_required
def supprimer_entretien(id):
    try:
        entretien = Entretien.query.get_or_404(id)
        db.session.delete(entretien)
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/entretiens/verification_km')
def verifier_kilometrages():
    # Récupérer les véhicules qui ont besoin d'un entretien
    entretiens = Entretien.query.all()
    vehicules_a_verifier = []
    
    for entretien in entretiens:
        vehicule = VehiculeGAR.query.get(entretien.vehicule_id)
        if vehicule:
            difference = entretien.prochain_kilometrage - vehicule.kilometrage_actuel
            if difference <= 1000:
                vehicules_a_verifier.append({
                    'matricule': vehicule.matricule,
                    'kilometrage_actuel': vehicule.kilometrage_actuel,
                    'kilometrage_prochain': entretien.prochain_kilometrage,
                    'difference': difference
                })
    
    return jsonify(vehicules_a_verifier)

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=3000)  # Port 3000 pour éviter les conflits